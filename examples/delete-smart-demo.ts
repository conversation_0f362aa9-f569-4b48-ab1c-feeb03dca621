/**
 * Demonstration of the smart delete translation tool
 * This script shows how to use the delete_translation_smart tool
 */

import { join } from 'path';
import { promises as fs } from 'fs';
import { SmartTranslationDeleter } from '../src/tools/delete-translation-smart.js';
import { TranslationIndex } from '../src/core/translation-index.js';
import { ServerConfig } from '../src/types/translation.js';

async function demonstrateSmartDeleter() {
  console.log('🗑️ Smart Translation Deleter Demo\n');

  // Create temp directory for demo
  const tempDir = join(process.cwd(), 'examples', 'temp-delete-demo');
  await fs.mkdir(tempDir, { recursive: true });

  // Configure the deleter
  const config: Required<ServerConfig> = {
    name: 'demo-server',
    version: '1.0.0',
    translationDir: tempDir,
    baseLanguage: 'en',
    debug: false,
    watchOptions: {
      debounceMs: 100,
      ignored: []
    },
    srcDir: '',
    exclude: [],
    autoSync: false,
    generateTypes: '',
    watchCode: false,
    projectRoot: '',
    frameworks: [],
    keyStyle: 'nested'
  };

  // Initialize index and deleter
  const index = new TranslationIndex({ baseLanguage: 'en' });
  const deleter = new SmartTranslationDeleter(index, config);

  try {
    console.log('📁 Setting up demo translations...');

    // Add some test translations
    index.set('common.buttons.save', 'en', 'Save');
    index.set('common.buttons.save', 'es', 'Guardar');
    index.set('common.buttons.save', 'fr', 'Sauvegarder');
    
    index.set('common.buttons.cancel', 'en', 'Cancel');
    index.set('common.buttons.cancel', 'es', 'Cancelar');
    
    index.set('common.messages.success', 'en', 'Success');
    index.set('common.messages.success', 'es', 'Éxito');
    
    index.set('auth.login.title', 'en', 'Sign In');
    index.set('auth.login.title', 'es', 'Iniciar Sesión');

    // Add parent keys for dependency testing
    index.set('common', 'en', 'Common translations');
    index.set('common', 'es', 'Traducciones comunes');

    // Create corresponding files
    const enData = {
      common: {
        buttons: {
          save: 'Save',
          cancel: 'Cancel'
        },
        messages: {
          success: 'Success'
        }
      },
      auth: {
        login: {
          title: 'Sign In'
        }
      }
    };

    const esData = {
      common: {
        buttons: {
          save: 'Guardar',
          cancel: 'Cancelar'
        },
        messages: {
          success: 'Éxito'
        }
      },
      auth: {
        login: {
          title: 'Iniciar Sesión'
        }
      }
    };

    const frData = {
      common: {
        buttons: {
          save: 'Sauvegarder'
        }
      }
    };

    await fs.writeFile(join(tempDir, 'en.json'), JSON.stringify(enData, null, 2));
    await fs.writeFile(join(tempDir, 'es.json'), JSON.stringify(esData, null, 2));
    await fs.writeFile(join(tempDir, 'fr.json'), JSON.stringify(frData, null, 2));

    console.log('✅ Demo translations set up successfully\n');

    // Demo 1: Simple deletion
    console.log('🔹 DEMO 1: Simple Key Deletion');
    console.log('==============================');
    console.log('Deleting "common.buttons.save" from all languages...\n');

    const result1 = await deleter.processSingleDeletion(
      { keyPath: 'common.buttons.save' },
      { dryRun: false, checkDependencies: false, writeToFiles: true, force: false }
    );

    console.log('Result:', JSON.stringify(result1, null, 2));
    console.log('\n');

    // Demo 2: Language-specific deletion
    console.log('🔹 DEMO 2: Language-Specific Deletion');
    console.log('=====================================');
    console.log('Deleting "common.buttons.cancel" from French only...\n');

    const result2 = await deleter.processSingleDeletion(
      { keyPath: 'common.buttons.cancel', languages: ['fr'] },
      { dryRun: false, checkDependencies: false, writeToFiles: true, force: false }
    );

    console.log('Result:', JSON.stringify(result2, null, 2));
    console.log('\n');

    // Demo 3: Dry run with dependency checking
    console.log('🔹 DEMO 3: Dry Run with Dependency Checking');
    console.log('============================================');
    console.log('Attempting to delete "common" (has child keys)...\n');

    const result3 = await deleter.processSingleDeletion(
      { keyPath: 'common' },
      { dryRun: true, checkDependencies: true, writeToFiles: false, force: false }
    );

    console.log('Result:', JSON.stringify(result3, null, 2));
    console.log('\n');

    // Demo 4: Forced deletion despite warnings
    console.log('🔹 DEMO 4: Forced Deletion Despite Warnings');
    console.log('===========================================');
    console.log('Force deleting "common" despite child key warnings...\n');

    const result4 = await deleter.processSingleDeletion(
      { keyPath: 'common' },
      { dryRun: false, checkDependencies: true, writeToFiles: true, force: true }
    );

    console.log('Result:', JSON.stringify(result4, null, 2));
    console.log('\n');

    // Demo 5: Bulk deletion
    console.log('🔹 DEMO 5: Bulk Deletion');
    console.log('========================');
    console.log('Bulk deleting multiple keys...\n');

    const deletions = [
      { keyPath: 'common.messages.success' },
      { keyPath: 'auth.login.title' },
      { keyPath: 'nonexistent.key' } // This will fail
    ];

    const response5 = await deleter.handleBulkDeletions(
      deletions,
      { dryRun: false, checkDependencies: false, writeToFiles: true, force: false, skipOnError: true, batchSize: 10 }
    );

    const result5 = JSON.parse(response5.content[0].text);
    console.log('Bulk deletion summary:');
    console.log('- Total:', result5.summary.total);
    console.log('- Successful:', result5.summary.successful);
    console.log('- Skipped:', result5.summary.skipped);
    console.log('- Failed:', result5.summary.failed);
    console.log('\nDetailed results:', JSON.stringify(result5.results, null, 2));
    console.log('\n');

    // Show final file states
    console.log('📄 FINAL FILE STATES');
    console.log('====================');
    
    try {
      const finalEn = await fs.readFile(join(tempDir, 'en.json'), 'utf-8');
      console.log('en.json:');
      console.log(finalEn);
      console.log('');
    } catch (error) {
      console.log('en.json: File not found or empty');
    }

    try {
      const finalEs = await fs.readFile(join(tempDir, 'es.json'), 'utf-8');
      console.log('es.json:');
      console.log(finalEs);
      console.log('');
    } catch (error) {
      console.log('es.json: File not found or empty');
    }

    try {
      const finalFr = await fs.readFile(join(tempDir, 'fr.json'), 'utf-8');
      console.log('fr.json:');
      console.log(finalFr);
      console.log('');
    } catch (error) {
      console.log('fr.json: File not found or empty');
    }

    console.log('🎯 SUMMARY');
    console.log('==========');
    console.log('✅ Demonstrated single key deletion');
    console.log('✅ Demonstrated language-specific deletion');
    console.log('✅ Demonstrated dry run with dependency checking');
    console.log('✅ Demonstrated forced deletion despite warnings');
    console.log('✅ Demonstrated bulk deletion with error handling');
    console.log('✅ Showed automatic file cleanup and empty object removal');

  } catch (error) {
    console.error('❌ Error running demo:', error);
  } finally {
    // Clean up temp directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
      console.log('\n🧹 Cleaned up demo files');
    } catch (error) {
      console.log('\n⚠️ Could not clean up demo files:', error);
    }
  }
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateSmartDeleter().catch(console.error);
}

export { demonstrateSmartDeleter };

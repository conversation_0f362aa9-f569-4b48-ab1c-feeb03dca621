/**
 * Demo: Search Missing Translations Tool
 * 
 * This example demonstrates how to use the search_missing_translations tool
 * to find translation keys that are used in code but missing from translation files.
 */

import { TranslationMCPServer } from '../src/server/mcp-server.js';
import { join } from 'path';
import { mkdir, writeFile, rm } from 'fs/promises';

async function runSearchMissingTranslationsDemo() {
  console.log('🔍 Search Missing Translations Tool Demo\n');

  // Create temporary demo directory
  const demoDir = join(process.cwd(), 'demo-missing-translations');
  const localesDir = join(demoDir, 'locales');
  const srcDir = join(demoDir, 'src');

  try {
    // Setup demo environment
    await mkdir(demoDir, { recursive: true });
    await mkdir(localesDir, { recursive: true });
    await mkdir(srcDir, { recursive: true });
    await mkdir(join(srcDir, 'components'), { recursive: true });

    console.log('📁 Setting up demo environment...');

    // Create translation files with some missing keys
    const enTranslations = {
      common: {
        buttons: {
          save: 'Save',
          cancel: 'Cancel',
          submit: 'Submit'
        },
        messages: {
          success: 'Success!',
          error: 'Error occurred'
        }
      },
      dashboard: {
        title: 'Dashboard',
        welcome: 'Welcome back'
      },
      auth: {
        login: {
          title: 'Login',
          email: 'Email',
          password: 'Password'
        }
      }
    };

    const esTranslations = {
      common: {
        buttons: {
          save: 'Guardar',
          cancel: 'Cancelar'
          // Missing: submit
        },
        messages: {
          success: '¡Éxito!'
          // Missing: error
        }
      },
      dashboard: {
        title: 'Panel de Control'
        // Missing: welcome
      }
      // Missing entire auth section
    };

    await writeFile(join(localesDir, 'en.json'), JSON.stringify(enTranslations, null, 2));
    await writeFile(join(localesDir, 'es.json'), JSON.stringify(esTranslations, null, 2));

    // Create source files with translation usage
    const userProfileComponent = `
import React from 'react';
import { useTranslation } from 'react-i18next';

export function UserProfile() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('dashboard.title')}</h1>
      <p>{t('dashboard.welcome')}</p>
      <p>{t('user.profile.bio')}</p> {/* Missing key */}
      <button>{t('common.buttons.save')}</button>
      <button>{t('common.buttons.delete')}</button> {/* Missing key */}
      <span>{t('notifications.count')}</span> {/* Missing key */}
    </div>
  );
}
`;

    const loginComponent = `
import React from 'react';
import { useTranslation } from 'react-i18next';

export function LoginForm() {
  const { t } = useTranslation();
  
  return (
    <form>
      <h2>{t('auth.login.title')}</h2>
      <input placeholder={t('auth.login.email')} />
      <input placeholder={t('auth.login.password')} />
      <button>{t('common.buttons.submit')}</button>
      <p>{t('auth.login.forgot_password')}</p> {/* Missing key */}
    </form>
  );
}
`;

    const vueComponent = `
<template>
  <div>
    <h1>{{ $t('dashboard.title') }}</h1>
    <p>{{ $t('pages.about.description') }}</p> <!-- Missing key -->
    <button>{{ $t('common.buttons.save') }}</button>
    <span>{{ $t('common.messages.loading') }}</span> <!-- Missing key -->
  </div>
</template>

<script>
export default {
  name: 'AboutPage'
}
</script>
`;

    await writeFile(join(srcDir, 'components', 'UserProfile.tsx'), userProfileComponent);
    await writeFile(join(srcDir, 'components', 'LoginForm.tsx'), loginComponent);
    await writeFile(join(srcDir, 'components', 'AboutPage.vue'), vueComponent);

    console.log('✅ Demo files created');

    // Initialize MCP server
    console.log('\n🚀 Initializing MCP server...');
    const server = new TranslationMCPServer({
      name: 'demo-server',
      version: '1.0.0',
      translationDir: localesDir,
      baseLanguage: 'en',
      srcDir,
      debug: false
    });

    // Initialize the translation index
    await server.initialize();
    console.log('✅ Server initialized');

    // Simulate tool call
    console.log('\n🔍 Searching for missing translations...');
    
    const result = await server.handleToolCall('search_missing_translations', {
      srcDir,
      frameworks: ['react', 'vue'],
      excludePatterns: ['node_modules', 'dist'],
      fileExtensions: ['.tsx', '.ts', '.vue'],
      includeDetails: true,
      groupByKey: true,
      maxDepth: 5
    });

    console.log('\n📊 Results:');
    console.log('='.repeat(50));
    
    const data = JSON.parse(result.content[0].text);
    
    console.log(`📁 Files scanned: ${data.totalFilesScanned}`);
    console.log(`🔑 Translation keys found: ${data.totalKeysFound}`);
    console.log(`❌ Missing keys: ${data.missingKeysCount}`);
    
    if (data.missingKeysCount > 0) {
      console.log('\n🚨 Missing Translation Keys:');
      console.log('-'.repeat(30));
      
      data.missingKeys.forEach((missing: any, index: number) => {
        console.log(`\n${index + 1}. ${missing.key}`);
        console.log(`   💡 ${missing.suggestion}`);
        
        if (missing.usages && missing.usages.length > 0) {
          console.log('   📍 Used in:');
          missing.usages.forEach((usage: any) => {
            console.log(`      • ${usage.filePath}:${usage.line}:${usage.column}`);
            console.log(`        Pattern: ${usage.pattern}`);
            if (usage.framework) {
              console.log(`        Framework: ${usage.framework}`);
            }
          });
        }
      });
    }

    console.log('\n📈 Framework Summary:');
    console.log('-'.repeat(20));
    Object.entries(data.frameworkSummary).forEach(([framework, count]) => {
      console.log(`${framework}: ${count} files`);
    });

    if (data.filesSummary && data.filesSummary.length > 0) {
      console.log('\n📄 Files with Issues:');
      console.log('-'.repeat(25));
      data.filesSummary.forEach((file: any) => {
        console.log(`\n📁 ${file.filePath}`);
        console.log(`   Framework: ${file.framework || 'unknown'}`);
        console.log(`   Total keys: ${file.totalKeys}`);
        console.log(`   Missing: ${file.missingKeys}`);
        if (file.missingKeyNames.length > 0) {
          console.log(`   Missing keys: ${file.missingKeyNames.join(', ')}`);
        }
      });
    }

    console.log('\n✨ Demo completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Use add_translation_smart to add missing translations');
    console.log('   2. Run check_translation_integrity to validate structure');
    console.log('   3. Integrate into your CI/CD pipeline');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    // Cleanup
    try {
      await rm(demoDir, { recursive: true, force: true });
      console.log('\n🧹 Demo files cleaned up');
    } catch (error) {
      console.warn('⚠️  Failed to cleanup demo files:', error);
    }
  }
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runSearchMissingTranslationsDemo().catch(console.error);
}

export { runSearchMissingTranslationsDemo };

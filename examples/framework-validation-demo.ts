/**
 * Demo: Framework Validation for Search Missing Translations Tool
 * 
 * This example demonstrates how the tool handles unsupported frameworks
 * and provides helpful error messages.
 */

import { setupSearchMissingTranslationsTool } from '../src/tools/search-missing-translations.js';
import { TranslationIndex } from '../src/core/translation-index.js';

async function runFrameworkValidationDemo() {
  console.log('🔍 Framework Validation Demo\n');

  // Create a mock server object to capture tool calls
  const mockServer = {
    tool: (name: string, description: string, schema: any, handler: any) => {
      console.log(`📋 Tool registered: ${name}`);
      console.log(`📝 Description: ${description}`);
      console.log(`🔧 Frameworks parameter: ${schema.frameworks.describe()}\n`);
      
      // Test the handler with unsupported frameworks
      return { name, description, schema, handler };
    }
  };

  // Create a basic translation index
  const index = new TranslationIndex({ baseLanguage: 'en' });
  index.set('common.buttons.save', 'en', 'Save');
  index.set('dashboard.title', 'en', 'Dashboard');

  const config = {
    baseLanguage: 'en',
    debug: false,
    srcDir: './src'
  };

  // Setup the tool
  console.log('🚀 Setting up search_missing_translations tool...');
  setupSearchMissingTranslationsTool(mockServer as any, index, config);

  // Get the registered tool
  const tool = (mockServer as any).tool.mock?.calls?.[0] || mockServer;
  
  if (tool.handler) {
    console.log('✅ Tool setup complete\n');
    
    // Test cases for framework validation
    const testCases = [
      {
        name: 'Supported frameworks only',
        frameworks: ['react', 'vue'],
        shouldSucceed: true
      },
      {
        name: 'Unsupported framework: nuxt',
        frameworks: ['nuxt', 'vue'],
        shouldSucceed: false
      },
      {
        name: 'Multiple unsupported frameworks',
        frameworks: ['nuxt', 'next', 'gatsby'],
        shouldSucceed: false
      },
      {
        name: 'Mixed supported and unsupported',
        frameworks: ['react', 'nuxt', 'vue'],
        shouldSucceed: false
      }
    ];

    for (const testCase of testCases) {
      console.log(`🧪 Testing: ${testCase.name}`);
      console.log(`   Frameworks: [${testCase.frameworks.join(', ')}]`);
      
      try {
        const result = await tool.handler({
          srcDir: './src',
          frameworks: testCase.frameworks,
          excludePatterns: ['node_modules'],
          fileExtensions: ['.tsx', '.vue'],
          includeDetails: true,
          groupByKey: true,
          maxDepth: 5
        });

        const data = JSON.parse(result.content[0].text);
        
        if (data.error) {
          console.log(`   ❌ Error (expected): ${data.error}`);
          console.log(`   🔧 Suggestions: ${data.suggestions}`);
          console.log(`   ✅ Supported: [${data.supportedFrameworks.join(', ')}]`);
        } else {
          console.log(`   ✅ Success: Scanned ${data.totalFilesScanned} files`);
          console.log(`   🔧 Used frameworks: [${data.scanConfig.frameworks.join(', ')}]`);
        }
        
      } catch (error) {
        console.log(`   💥 Unexpected error: ${error}`);
      }
      
      console.log('');
    }

    console.log('📚 Framework Mapping Guide:');
    console.log('─'.repeat(40));
    console.log('• Nuxt      → use "vue"');
    console.log('• Next.js   → use "react"');
    console.log('• Gatsby    → use "react"');
    console.log('• SvelteKit → use "svelte"');
    console.log('• Vite      → use underlying framework');
    console.log('• Webpack   → use underlying framework');
    
    console.log('\n✨ Framework validation demo completed!');
    
  } else {
    console.log('❌ Failed to setup tool');
  }
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runFrameworkValidationDemo().catch(console.error);
}

export { runFrameworkValidationDemo };

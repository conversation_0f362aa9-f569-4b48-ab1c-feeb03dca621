/**
 * Tests for reorganize translation files tool
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';
import { TranslationFileReorganizer } from '../../src/tools/reorganize-translation-files.js';
import { ServerConfig } from '../../src/types/translation.js';

describe('TranslationFileReorganizer', () => {
  let tempDir: string;
  let config: Required<ServerConfig>;
  let reorganizer: TranslationFileReorganizer;

  beforeEach(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(join(tmpdir(), 'i18n-test-'));
    
    config = {
      name: 'test-server',
      version: '1.0.0',
      translationDir: tempDir,
      baseLanguage: 'en',
      debug: false,
      watchOptions: {
        debounceMs: 100,
        ignored: []
      },
      srcDir: undefined,
      exclude: [],
      autoSync: false,
      generateTypes: undefined,
      watchCode: false,
      projectRoot: process.cwd(),
      frameworks: [],
      keyStyle: 'nested'
    };

    reorganizer = new TranslationFileReorganizer(config);
  });

  afterEach(async () => {
    // Clean up temporary directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('reorganizeFiles', () => {
    it('should reorganize files to match base language structure', async () => {
      // Create base language file (en.json)
      const baseTranslations = {
        common: {
          buttons: {
            save: 'Save',
            cancel: 'Cancel',
            submit: 'Submit'
          },
          messages: {
            success: 'Success!',
            error: 'Error occurred'
          }
        },
        pages: {
          home: {
            title: 'Home Page',
            welcome: 'Welcome'
          }
        }
      };

      // Create disorganized French file with different order and missing/extra keys
      const frenchTranslations = {
        pages: {
          home: {
            welcome: 'Bienvenue',
            title: 'Page d\'accueil'
          }
        },
        common: {
          messages: {
            error: 'Erreur survenue'
            // missing 'success' key
          },
          buttons: {
            cancel: 'Annuler',
            save: 'Enregistrer'
            // missing 'submit' key
          },
          extra: {
            unused: 'Not needed'
          }
        }
      };

      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify(baseTranslations, null, 2)
      );

      await fs.writeFile(
        join(tempDir, 'fr.json'),
        JSON.stringify(frenchTranslations, null, 2)
      );

      // Run reorganization
      const result = await reorganizer.reorganizeFiles({
        baseLanguage: 'en',
        dryRun: false,
        backupFiles: false,
        preserveComments: true,
        includeDetails: true
      });

      // Verify results
      expect(result.success).toBe(true);
      expect(result.summary.filesReorganized).toBe(1);
      expect(result.summary.keysAdded).toBe(2); // success, submit
      expect(result.summary.keysRemoved).toBe(1); // extra.unused

      // Verify the reorganized file structure
      const reorganizedContent = await fs.readFile(join(tempDir, 'fr.json'), 'utf-8');
      const reorganizedData = JSON.parse(reorganizedContent);

      // Check structure matches base language order
      const expectedStructure = {
        common: {
          buttons: {
            save: 'Enregistrer',
            cancel: 'Annuler',
            submit: 'Submit' // placeholder from base language
          },
          messages: {
            success: 'Success!', // placeholder from base language
            error: 'Erreur survenue'
          }
        },
        pages: {
          home: {
            title: 'Page d\'accueil',
            welcome: 'Bienvenue'
          }
        }
      };

      expect(reorganizedData).toEqual(expectedStructure);

      // Verify key order matches base language
      expect(Object.keys(reorganizedData)).toEqual(['common', 'pages']);
      expect(Object.keys(reorganizedData.common)).toEqual(['buttons', 'messages']);
      expect(Object.keys(reorganizedData.common.buttons)).toEqual(['save', 'cancel', 'submit']);
    });

    it('should handle dry run mode correctly', async () => {
      const baseTranslations = { test: 'Test' };
      const frenchTranslations = { extra: 'Extra', test: 'Test FR' };

      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify(baseTranslations, null, 2)
      );

      await fs.writeFile(
        join(tempDir, 'fr.json'),
        JSON.stringify(frenchTranslations, null, 2)
      );

      // Run dry run
      const result = await reorganizer.reorganizeFiles({
        baseLanguage: 'en',
        dryRun: true,
        backupFiles: false,
        preserveComments: true,
        includeDetails: true
      });

      // Verify results show what would happen
      expect(result.success).toBe(true);
      expect(result.summary.filesReorganized).toBe(1);
      expect(result.summary.keysRemoved).toBe(1);

      // Verify original file is unchanged
      const originalContent = await fs.readFile(join(tempDir, 'fr.json'), 'utf-8');
      const originalData = JSON.parse(originalContent);
      expect(originalData).toEqual(frenchTranslations);

      // Verify recommendations mention dry run
      expect(result.recommendations.some(r => r.includes('dry run'))).toBe(true);
    });

    it('should skip files with no changes needed', async () => {
      const baseTranslations = {
        common: {
          save: 'Save',
          cancel: 'Cancel'
        }
      };

      // French file already has correct structure
      const frenchTranslations = {
        common: {
          save: 'Enregistrer',
          cancel: 'Annuler'
        }
      };

      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify(baseTranslations, null, 2)
      );

      await fs.writeFile(
        join(tempDir, 'fr.json'),
        JSON.stringify(frenchTranslations, null, 2)
      );

      const result = await reorganizer.reorganizeFiles({
        baseLanguage: 'en',
        dryRun: false,
        backupFiles: false,
        preserveComments: true,
        includeDetails: true
      });

      expect(result.success).toBe(true);
      expect(result.summary.filesReorganized).toBe(0);
      expect(result.summary.filesSkipped).toBe(1);
      expect(result.fileResults.fr.skipReason).toBe('No changes needed');
    });

    it('should handle invalid JSON files gracefully', async () => {
      const baseTranslations = { test: 'Test' };

      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify(baseTranslations, null, 2)
      );

      // Create invalid JSON file
      await fs.writeFile(
        join(tempDir, 'fr.json'),
        '{ invalid json'
      );

      const result = await reorganizer.reorganizeFiles({
        baseLanguage: 'en',
        dryRun: false,
        backupFiles: false,
        preserveComments: true,
        includeDetails: true
      });

      expect(result.success).toBe(false);
      expect(result.summary.errors).toBe(1);
      expect(result.fileResults.fr.skipReason).toBe('Invalid JSON syntax');
      expect(result.fileResults.fr.error).toBeDefined();
    });

    it('should only reorganize specified files when onlyReorganizeFiles is provided', async () => {
      const baseTranslations = { test: 'Test' };

      await fs.writeFile(
        join(tempDir, 'en.json'),
        JSON.stringify(baseTranslations, null, 2)
      );

      await fs.writeFile(
        join(tempDir, 'fr.json'),
        JSON.stringify({ extra: 'Extra', test: 'Test FR' }, null, 2)
      );

      await fs.writeFile(
        join(tempDir, 'de.json'),
        JSON.stringify({ extra: 'Extra', test: 'Test DE' }, null, 2)
      );

      const result = await reorganizer.reorganizeFiles({
        baseLanguage: 'en',
        dryRun: false,
        backupFiles: false,
        preserveComments: true,
        includeDetails: true,
        onlyReorganizeFiles: ['fr'] // Only reorganize French
      });

      expect(result.success).toBe(true);
      expect(result.summary.filesReorganized).toBe(1);
      expect(result.fileResults.fr).toBeDefined();
      expect(result.fileResults.de).toBeUndefined(); // Should not be processed
    });
  });
});

<!-- ServiceBasicInfo.vue -->
<template>
  <div class="bg-white shadow-md rounded-lg p-2">
    <p class="mt-4 text-gray-500">{{ t('dashboard.services.form.fillDetails') }}</p>

    <div class="grid grid-cols-1 gap-4 mb-6">
      <!-- Service Title, Category, and Subcategory inputs -->
      <div class="flex flex-col md:flex-row gap-2">
        <!-- Service Title -->
        <label
          for="serviceProductTitle"
          class="sr-only"
          >{{ t('dashboard.services.form.fillDetails') }}</label
        >
        <InputText
          id="serviceProductTitle"
          type="text"
          placeholder="Service Title"
          class="w-full"
          v-model="modelValue.title"
          @update:modelValue="updateService"
          :class="{ 'p-invalid': submitted && !modelValue.title }"
        />

        <!-- Category dropdown -->
        <label
          for="category"
          class="sr-only"
          >{{ t('dashboard.services.category') }}</label
        >
        <Select
          id="category"
          class="w-full"
          :options="sortedCategories"
          option-value="id"
          v-model="modelValue.categoryId"
          :placeholder="'Category'"
          @update:modelValue="updateCategory"
          :class="{ 'p-invalid': submitted && !modelValue.categoryId }"
        >
          <template #value="slotProps">
            <div
              v-if="slotProps.value"
              class="flex items-center"
            >
              <div>{{ t(`categories.${slotProps.value}`) }}</div>
            </div>
            <span
              v-else
              aria-live="polite"
              aria-atomic="true"
            >
              {{ slotProps.placeholder }}
            </span>
          </template>
          <template #option="slotProps">
            <div
              class="flex items-center"
              role="option"
              :aria-selected="slotProps.selected"
            >
              <div>{{ t(`categories.${slotProps.option.id}`) }}</div>
            </div>
          </template>
        </Select>

        <!-- Subcategory dropdown -->
        <label
          for="subCategory"
          class="sr-only"
          >{{ t('dashboard.services.subCategory') }}</label
        >
        <Select
          id="subCategory"
          class="w-full"
          :options="sortedSubCategories"
          option-value="id"
          :placeholder="'Subcategory'"
          v-model="modelValue.subcategoryId"
          @update:modelValue="updateService"
          :class="{ 'p-invalid': submitted && !modelValue.subcategoryId }"
        >
          <template #value="slotProps">
            <div
              v-if="slotProps.value"
              class="flex items-center"
            >
              <div>{{ t(`subCategories.${modelValue.categoryId}.${slotProps.value}`) }}</div>
            </div>
            <span v-else>
              {{ slotProps.placeholder }}
            </span>
          </template>
          <template #option="slotProps">
            <div class="flex items-center">
              <div>{{ t(`subCategories.${modelValue.categoryId}.${slotProps.option.id}`) }}</div>
            </div>
          </template>
        </Select>
      </div>

      <!-- Rich text editor for service description -->
      <Editor
        v-model="modelValue.description"
        editorStyle="height: 220px;"
        @update:modelValue="updateService"
        :pt="{
          root: {
            class: { 'border-red-500 border': submitted && !modelValue.description?.trim() },
          },
        }"
      >
        <template #toolbar>
          <span class="ql-formats">
            <select class="ql-size">
              <option value="small"></option>
              <option></option>
              <option value="large"></option>
              <option value="huge"></option>
            </select>
          </span>
          <span class="ql-formats">
            <div
              class="relative inline-block"
              @mouseenter="tooltips.bold = true"
              @mouseleave="tooltips.bold = false"
            >
              <button class="ql-bold"></button>
              <div
                v-if="tooltips.bold"
                class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 p-2 text-sm bg-gray-800 text-white rounded-lg shadow-lg z-50 whitespace-nowrap"
              >
                Bold
              </div>
            </div>
            <div
              class="relative inline-block"
              @mouseenter="tooltips.italic = true"
              @mouseleave="tooltips.italic = false"
            >
              <button class="ql-italic"></button>
              <div
                v-if="tooltips.italic"
                class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 p-2 text-sm bg-gray-800 text-white rounded-lg shadow-lg z-50 whitespace-nowrap"
              >
                Italic
              </div>
            </div>
            <div
              class="relative inline-block"
              @mouseenter="tooltips.underline = true"
              @mouseleave="tooltips.underline = false"
            >
              <button class="ql-underline"></button>
              <div
                v-if="tooltips.underline"
                class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 p-2 text-sm bg-gray-800 text-white rounded-lg shadow-lg z-50 whitespace-nowrap"
              >
                Underline
              </div>
            </div>
          </span>
          <span class="ql-formats">
            <div
              class="relative inline-block"
              @mouseenter="tooltips.orderedList = true"
              @mouseleave="tooltips.orderedList = false"
            >
              <button
                class="ql-list"
                value="ordered"
              ></button>
              <div
                v-if="tooltips.orderedList"
                class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 p-2 text-sm bg-gray-800 text-white rounded-lg shadow-lg z-50 whitespace-nowrap"
              >
                List
              </div>
            </div>
            <div
              class="relative inline-block"
              @mouseenter="tooltips.bulletList = true"
              @mouseleave="tooltips.bulletList = false"
            >
              <button
                class="ql-list"
                value="bullet"
              ></button>
              <div
                v-if="tooltips.bulletList"
                class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 p-2 text-sm bg-gray-800 text-white rounded-lg shadow-lg z-50 whitespace-nowrap"
              >
                Bullet
              </div>
            </div>
          </span>
        </template>
      </Editor>

      <!-- Positive keywords section -->
      <div class="flex flex-col">
        <p class="font-semibold">{{ t('dashboard.services.form.positiveKeywords') }}</p>
        <p class="font-light text-gray-600 mb-2">
          {{ t('dashboard.services.form.enterKeywords') }}
        </p>
        <div>
          <!-- Input for adding new tags -->
          <div
            class="flex mb-2 h-9"
            id="tagName"
          >
            <InputText
              v-model="tag"
              type="text"
              class="w-full p-2 border rounded-md"
              :placeholder="'Tags'"
              @keyup.enter="addTags"
            />
            <Button
              @click="addTags"
              class="ml-2 !text-white"
            >
              {{ t('common.add') }}
            </Button>
          </div>
          <!-- Display added tags -->
          <div class="flex flex-wrap gap-2">
            <Chip
              v-for="(tag, index) in modelValue.positiveKeywords"
              :key="String(tag)"
              :label="String(tag)"
              removable
              class="!bg-dblue-500 text-sm"
              :pt="{
                label: { class: 'font-semibold text-white' },
                removeIcon: { style: { color: 'red' } },
              }"
              @remove="deleteTag(index)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ServiceProductClient } from '~~/types/serviceProduct.type';
// Editor is auto-imported by Nuxt, no need to import it manually

const props = defineProps<{
  modelValue: ServiceProductClient;
  categories: any[];
  subCategories: any[];
  submitted: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: ServiceProductClient): void;
  (e: 'update:category', value: number): void;
}>();

const { t, locale } = useI18n();
const tag = ref('');

const tooltips = ref({
  bold: false,
  italic: false,
  underline: false,
  orderedList: false,
  bulletList: false,
});

// Ductize Rule: Sort dropdowns alphabetically by i18n display name (OK)
const sortedCategories = computed(() => {
  return [...props.categories].sort((a, b) => {
    const nameA = t(`categories.${a.id}`);
    const nameB = t(`categories.${b.id}`);
    return nameA.localeCompare(nameB, locale.value);
  });
});

const sortedSubCategories = computed(() => {
  if (!props.modelValue.categoryId) return [];
  return [...props.subCategories].sort((a, b) => {
    const nameA = t(`subCategories.${props.modelValue.categoryId}.${a.id}`);
    const nameB = t(`subCategories.${props.modelValue.categoryId}.${b.id}`);
    return nameA.localeCompare(nameB, locale.value);
  });
});

function updateService() {
  emit('update:modelValue', props.modelValue);
}

function updateCategory(value: number) {
  emit('update:category', value);
  updateService();
}

function addTags() {
  if (tag.value) {
    props.modelValue.positiveKeywords.push(tag.value);
    tag.value = '';
    updateService();
  }
}

function deleteTag(index: number) {
  props.modelValue.positiveKeywords.splice(index, 1);
  updateService();
}
</script>

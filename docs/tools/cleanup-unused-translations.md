# Cleanup Unused Translations Tool

The `cleanup_unused_translations` tool helps you find and optionally delete translation keys from the source of truth translation file that are not being used anywhere in your codebase. This tool is essential for maintaining clean translation files and reducing bundle size.

## Overview

This tool performs a comprehensive scan of your source code to identify which translation keys are actually being used, then compares this with your source of truth translation file (typically English) to find unused keys. It includes sophisticated risk assessment to avoid accidentally deleting keys that might be used dynamically.

## Key Features

- **Smart Code Analysis**: Uses the same robust code analyzer as other tools to detect translation usage across multiple frameworks
- **Advanced Dynamic Translation Detection**: Detects complex dynamic usage patterns including:
  - Template literals: `t(\`subCategories.${categoryId}.${itemId}\`)`
  - String concatenation: `t(prefix + variable + suffix)`
  - Variable construction: `const key = \`prefix.${id}.suffix\`; t(key)`
  - Object property access: `translations[category][id]`
  - Array join patterns: `[prefix, variable, suffix].join('.')`
- **Risk Assessment**: Categorizes unused keys by risk level (low, medium, high) to help you make informed decisions
- **Dry Run Mode**: Preview what would be deleted before making any changes
- **Framework Support**: Works with React, Vue, Svelte, and Angular applications
- **Nested Key Handling**: Intelligently handles parent-child relationships in translation keys
- **Batch Operations**: Can delete multiple unused keys at once

## Usage

### Basic Usage

```typescript
// Find unused translations (dry run by default)
const result = await mcpClient.callTool('cleanup_unused_translations', {
  srcDir: './src',
  sourceLanguage: 'en'
});
```

### Advanced Configuration

```typescript
const result = await mcpClient.callTool('cleanup_unused_translations', {
  srcDir: './src',
  sourceLanguage: 'en',
  frameworks: ['react', 'vue'],
  excludePatterns: ['node_modules', 'dist', 'test'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx', '.vue'],
  maxDepth: 5,
  dryRun: false,
  includeChildren: true,
  riskThreshold: 'low',
  autoDelete: true
});
```

## Parameters

### Required Parameters

None - all parameters have sensible defaults.

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `srcDir` | string | `config.srcDir \|\| './src'` | Source directory to scan for translation usage. Can be relative (e.g., "app", "./src") or absolute path (e.g., "/Users/<USER>/project/app") |
| `sourceLanguage` | string | `'en'` | Source of truth language to cleanup |
| `frameworks` | array | `['react', 'vue', 'svelte', 'angular']` | Frameworks to analyze |
| `excludePatterns` | array | `['node_modules', 'dist', 'build', '.git']` | File/directory patterns to exclude |
| `fileExtensions` | array | `['.ts', '.tsx', '.js', '.jsx', '.vue', '.svelte', '.html']` | File extensions to scan |
| `maxDepth` | number | `5` | Maximum directory depth to scan |
| `dryRun` | boolean | `true` | Preview mode - show what would be deleted without actually deleting |
| `includeChildren` | boolean | `false` | Include analysis of nested/child keys |
| `riskThreshold` | string | `'medium'` | Only suggest deletion for keys at or below this risk level |
| `autoDelete` | boolean | `false` | Automatically delete unused keys (only works when dryRun is false) |

## Risk Assessment

The tool categorizes unused keys into three risk levels:

### Low Risk (Safe to Delete)
- Leaf keys with no children
- No dynamic patterns detected
- Not in common/shared namespaces

### Medium Risk (Review Recommended)
- Parent keys with few children
- Keys in common/shared/global namespaces
- Top-level keys that might be used as namespaces

### High Risk (Careful Review Required)
- **Dynamic Usage Detected**: Keys found to be used in dynamic patterns in the codebase
- Parent keys with many children (>5)
- Keys containing dynamic patterns (${}, {{}}, etc.)
- Values suggesting programmatic usage

## Dynamic Translation Detection

The tool includes sophisticated detection for dynamic translation usage patterns that are commonly missed by simple static analysis:

### Detected Patterns

1. **Template Literals**
   ```javascript
   t(`subCategories.${props.modelValue.categoryId}.${a.id}`)
   t(`errors.${errorType}.message`)
   ```

2. **String Concatenation**
   ```javascript
   t('prefix.' + dynamicPart + '.suffix')
   t(baseKey + '.' + itemId)
   ```

3. **Variable Construction**
   ```javascript
   const translationKey = `categories.${category}.title`;
   t(translationKey);
   ```

4. **Object Property Access**
   ```javascript
   translations[category][subcategory]
   i18n.t(keys[section][item])
   ```

5. **Array Join Patterns**
   ```javascript
   const key = ['forms', formType, 'validation'].join('.');
   t(key);
   ```

### How It Works

When analyzing unused keys, the tool:

1. **Scans Source Files**: Examines up to 50 source files for dynamic patterns
2. **Pattern Matching**: Uses regex patterns to detect various dynamic usage styles
3. **Evidence Collection**: Collects specific examples of where dynamic usage was found
4. **Risk Elevation**: Automatically marks keys as high-risk if dynamic usage is detected

### Example Output

```json
{
  "key": "subCategories.electronics.phones",
  "riskLevel": "high",
  "riskReason": "Potentially used dynamically in code: Found dynamic pattern in src/components/ProductList.vue: t(`subCategories.${props.modelValue.categoryId}.${a.id}`)..."
}
```

## Response Format

```typescript
{
  "totalFilesScanned": 150,
  "totalKeysFoundInCode": 89,
  "totalKeysInSource": 120,
  "unusedKeysCount": 31,
  "unusedKeys": [
    {
      "key": "old.feature.title",
      "value": "Old Feature",
      "hasChildren": false,
      "suggestion": "Delete unused key \"old.feature.title\"",
      "riskLevel": "low",
      "riskReason": "Leaf key with no apparent dynamic usage"
    }
  ],
  "usedKeys": ["common.save", "common.cancel", ...],
  "frameworkSummary": {
    "react": 45,
    "vue": 30
  },
  "scanConfig": {
    "srcDir": "/path/to/src",
    "sourceLanguage": "en",
    "frameworks": ["react", "vue"],
    "excludePatterns": ["node_modules"],
    "fileExtensions": [".ts", ".tsx", ".vue"]
  },
  "dryRun": true,
  "deletionResults": [] // Only present when dryRun is false and autoDelete is true
}
```

## Examples

### Safe Cleanup (Low Risk Only)

```typescript
// Only suggest deletion of low-risk keys
const result = await mcpClient.callTool('cleanup_unused_translations', {
  riskThreshold: 'low',
  dryRun: true
});

console.log(`Found ${result.unusedKeysCount} low-risk unused keys`);
```

### Preview Before Deletion

```typescript
// First, preview what would be deleted
const preview = await mcpClient.callTool('cleanup_unused_translations', {
  dryRun: true,
  riskThreshold: 'medium'
});

console.log('Keys that would be deleted:', preview.unusedKeys.map(k => k.key));

// Then actually delete if you're satisfied
if (confirm('Delete these keys?')) {
  const deletion = await mcpClient.callTool('cleanup_unused_translations', {
    dryRun: false,
    autoDelete: true,
    riskThreshold: 'medium'
  });
  
  console.log('Deletion results:', deletion.deletionResults);
}
```

### Framework-Specific Cleanup

```typescript
// Only analyze React files
const result = await mcpClient.callTool('cleanup_unused_translations', {
  frameworks: ['react'],
  fileExtensions: ['.tsx', '.jsx'],
  excludePatterns: ['node_modules', 'dist', 'test', 'stories']
});
```

## Best Practices

### 1. Always Start with Dry Run
Never skip the dry run mode on your first attempt. Review the results carefully.

### 2. Use Conservative Risk Thresholds
Start with `riskThreshold: 'low'` and gradually increase as you gain confidence.

### 3. Review Dynamic Patterns
Pay special attention to keys flagged as high risk due to dynamic patterns.

### 4. Test After Cleanup
Always test your application thoroughly after deleting translation keys.

### 5. Version Control
Commit your changes before running cleanup operations so you can easily revert if needed.

## Common Scenarios

### Cleaning Up After Feature Removal

```typescript
// After removing a feature, clean up its translations
const result = await mcpClient.callTool('cleanup_unused_translations', {
  dryRun: true
});

// Look for keys related to the removed feature
const featureKeys = result.unusedKeys.filter(k => 
  k.key.includes('oldFeature') || k.key.includes('deprecated')
);
```

### Preparing for Production

```typescript
// Conservative cleanup before production release
const result = await mcpClient.callTool('cleanup_unused_translations', {
  riskThreshold: 'low',
  dryRun: false,
  autoDelete: true
});

console.log(`Cleaned up ${result.deletionResults?.filter(r => r.success).length} unused keys`);
```

## Troubleshooting

### Path Resolution Issues

If you get "No source files found" errors:

**Error Example:**
```
No source files found!

📁 Path Resolution:
  - Original srcDir: "app" (relative)
  - Resolved to: "/some/path/app"
  - Directory exists: false
```

**Solutions:**

1. **Use Absolute Paths** (Recommended):
   ```json
   {
     "srcDir": "/Users/<USER>/project/app"
   }
   ```

2. **Use Relative Paths from Project Root**:
   ```json
   {
     "srcDir": "./app",
     "srcDir": "src/components"
   }
   ```

3. **Check Directory Exists**:
   - Verify the directory actually exists
   - Check spelling and case sensitivity
   - Ensure you have read permissions

4. **Verify File Extensions**:
   ```json
   {
     "fileExtensions": [".vue", ".ts", ".js", ".jsx", ".tsx"]
   }
   ```

5. **Check Exclude Patterns**:
   - Make sure exclude patterns aren't filtering out your source directory
   - Common issue: having "app" in exclude patterns when srcDir is "app"

### False Positives

If the tool suggests deleting keys that you know are used:

1. Check if the usage is in excluded directories
2. Verify the file extensions are included in the scan
3. Look for dynamic key construction that the analyzer might miss
4. Consider if the key is used in external files or configurations

### Performance with Large Codebases

For very large codebases:

1. Reduce `maxDepth` to limit directory traversal
2. Use more specific `excludePatterns`
3. Limit `fileExtensions` to only necessary types
4. Consider scanning specific subdirectories instead of the entire codebase

## Related Tools

- `search_missing_translations`: Find keys used in code but missing from translations
- `delete_translation_smart`: Delete specific translation keys
- `check_translation_integrity`: Validate translation file structure
- `reorganize_translation_files`: Reorganize translation file structure

# Search Missing Translations Tool

The `search_missing_translations` tool is a powerful utility that scans your entire codebase to find translation keys that are referenced in your source code but are missing from your translation files.

## Overview

This tool addresses a common problem in internationalized applications: translation keys that are called in the code but don't exist in the translation files. This can lead to missing text, broken user interfaces, or runtime errors.

## Features

- **Multi-framework support**: Works with React, Vue, Svelte, and Angular
  - **Note**: For Nuxt projects, use "vue" framework (Nuxt uses .vue files)
  - **Note**: For Next.js projects, use "react" framework
- **Comprehensive scanning**: Recursively scans source directories
- **Smart pattern detection**: Uses framework-specific patterns to find translation usage
- **False positive prevention**: Avoids matching non-translation function calls like `emit()`, `submit()`, `commit()`
- **Detailed reporting**: Provides file locations, line numbers, and usage patterns
- **Grouping options**: Can group results by translation key or show flat list
- **Exclude patterns**: Supports excluding directories like `node_modules`
- **Performance optimized**: Processes files in batches with configurable depth limits
- **Framework validation**: Provides helpful error messages for unsupported frameworks

## Usage

### Basic Usage

```json
{
  "tool": "search_missing_translations",
  "arguments": {}
}
```

### Advanced Usage

```json
{
  "tool": "search_missing_translations",
  "arguments": {
    "srcDir": "./src",
    "frameworks": ["react", "vue"],
    "excludePatterns": ["node_modules", "dist", "build"],
    "fileExtensions": [".ts", ".tsx", ".js", ".jsx", ".vue"],
    "includeDetails": true,
    "groupByKey": true,
    "maxDepth": 5
  }
}
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `srcDir` | string | config.srcDir or "./src" | Source directory to scan |
| `frameworks` | array | ["react", "vue", "svelte", "angular"] | Frameworks to analyze |
| `excludePatterns` | array | ["node_modules", "dist", "build", ".git"] | Patterns to exclude |
| `fileExtensions` | array | [".ts", ".tsx", ".js", ".jsx", ".vue", ".svelte", ".html"] | File extensions to scan |
| `includeDetails` | boolean | true | Include detailed location information |
| `groupByKey` | boolean | true | Group results by translation key |
| `maxDepth` | number | 5 | Maximum directory depth to scan |

## Output Format

### Grouped Output (default)

```json
{
  "totalFilesScanned": 25,
  "totalKeysFound": 8,
  "missingKeysCount": 3,
  "missingKeys": [
    {
      "key": "user.profile.title",
      "usages": [
        {
          "filePath": "components/UserProfile.tsx",
          "line": 15,
          "column": 12,
          "pattern": "t('user.profile.title')",
          "framework": "react"
        }
      ],
      "suggestion": "Add translation for \"user.profile.title\" - used in 1 file"
    }
  ],
  "filesSummary": [
    {
      "filePath": "components/UserProfile.tsx",
      "framework": "react",
      "totalKeys": 5,
      "missingKeys": 2,
      "missingKeyNames": ["user.profile.title", "user.profile.bio"]
    }
  ],
  "frameworkSummary": {
    "react": 15,
    "vue": 10
  },
  "scanConfig": {
    "srcDir": "./src",
    "frameworks": ["react", "vue"],
    "excludePatterns": ["node_modules"],
    "fileExtensions": [".tsx", ".vue"]
  }
}
```

### Flat Output (groupByKey: false)

```json
{
  "totalFilesScanned": 25,
  "totalKeysFound": 8,
  "missingKeysCount": 3,
  "missingKeys": [
    {
      "key": "user.profile.title",
      "filePath": "components/UserProfile.tsx",
      "line": 15,
      "column": 12,
      "pattern": "t('user.profile.title')",
      "framework": "react",
      "suggestion": "Add translation for \"user.profile.title\" - used in 1 file"
    }
  ]
}
```

## Framework Support

### Supported Frameworks

| Framework | Use | File Extensions | Translation Patterns |
|-----------|-----|----------------|---------------------|
| `react` | React, Next.js, Gatsby | `.tsx`, `.jsx`, `.ts`, `.js` | `t('key')`, `useTranslation().t('key')`, `i18n.t('key')` |
| `vue` | Vue, Nuxt | `.vue`, `.ts`, `.js` | `$t('key')`, `this.$t('key')`, `t('key')` |
| `svelte` | Svelte, SvelteKit | `.svelte`, `.ts`, `.js` | `$_('key')`, `$t('key')` |
| `angular` | Angular | `.ts`, `.html`, `.js` | `translate.get('key')`, `translate.instant('key')`, `\| translate:'key'` |

### Framework Mapping

If you're using a meta-framework, use the underlying framework:

- **Nuxt** → Use `vue` (Nuxt uses Vue files)
- **Next.js** → Use `react` (Next.js uses React)
- **Gatsby** → Use `react` (Gatsby uses React)
- **SvelteKit** → Use `svelte` (SvelteKit uses Svelte)

### Unsupported Framework Error

If you specify an unsupported framework like "nuxt", you'll get a helpful error message:

```json
{
  "error": "Unsupported framework(s) detected",
  "unsupportedFrameworks": ["nuxt"],
  "supportedFrameworks": ["react", "vue", "svelte", "angular"],
  "suggestions": "\"nuxt\" -> use \"vue (Nuxt uses Vue files)\"",
  "message": "Please use one of the supported frameworks: react, vue, svelte, angular"
}
```

### Translation Patterns by Framework

#### React
- `t('key')` - react-i18next
- `useTranslation().t('key')`
- `i18n.t('key')`
- `$t('key')`

#### Vue
- `$t('key')` - vue-i18n
- `this.$t('key')`
- `t('key')`

#### Svelte
- `$_('key')` - svelte-i18n
- `$t('key')`

#### Angular
- `translate.get('key')`
- `translate.instant('key')`
- `| translate:'key'`

## Common Use Cases

### 1. Pre-deployment Check
Run before deploying to catch missing translations:

```bash
# Check for missing translations in production build
search_missing_translations --srcDir ./src --excludePatterns "node_modules,test"
```

### 2. Code Review Integration
Use in CI/CD pipelines to ensure all translation keys exist:

```bash
# Fail build if missing translations found
if [ "$(search_missing_translations | jq '.missingKeysCount')" -gt 0 ]; then
  echo "Missing translations found!"
  exit 1
fi
```

### 3. Migration Assistance
When refactoring or migrating translation systems:

```bash
# Find all translation usage patterns
search_missing_translations --includeDetails true --groupByKey false
```

### 4. Framework-specific Analysis
Analyze specific frameworks separately:

```bash
# Only check React components
search_missing_translations --frameworks "react" --fileExtensions ".tsx,.jsx"
```

## Performance Tips

1. **Use exclude patterns**: Always exclude `node_modules`, `dist`, and other build directories
2. **Limit file extensions**: Only scan relevant file types for your project
3. **Adjust max depth**: Reduce `maxDepth` for very deep directory structures
4. **Framework filtering**: Specify only the frameworks you actually use

## Error Handling

The tool handles various error conditions gracefully:

- **No source files found**: Returns clear error message
- **Invalid file paths**: Skips unreadable files and continues
- **Parse errors**: Logs warnings but continues processing other files
- **Permission errors**: Skips inaccessible directories

## Integration Examples

### With npm scripts

```json
{
  "scripts": {
    "check-translations": "i18n-mcp search_missing_translations",
    "check-translations:react": "i18n-mcp search_missing_translations --frameworks react"
  }
}
```

### With GitHub Actions

```yaml
- name: Check for missing translations
  run: |
    npm run check-translations
    if [ $? -ne 0 ]; then
      echo "Missing translations detected"
      exit 1
    fi
```

## Related Tools

- `check_translation_integrity` - Validates translation file structure
- `analyze_code_file` - Analyzes individual files for translation usage
- `add_translation_smart` - Adds missing translations
- `extract_to_translation` - Extracts hardcoded strings to translations

## Pattern Matching Details

The tool uses sophisticated regex patterns to accurately identify translation function calls while avoiding false positives:

### What Gets Detected ✅
- `t('translation.key')` - Standalone translation calls
- `const text = t('message');` - Assignment contexts
- `{t('ui.label')}` - JSX expressions
- `return t('error.message');` - Return statements
- `console.log(t('debug.info'));` - Function arguments

### What Gets Ignored ❌

**Function Call False Positives:**
- `emit('event-name')` - Event emission
- `submit('form-data')` - Form submission
- `commit('state-change')` - State management
- `this.t('method.call')` - Method calls on objects
- `obj.t('property.access')` - Property access
- `component.t('component.method')` - Component methods

**Dynamic Translation Keys:**
- `t(\`languages.\${language}\`)` - Template literals with variables
- `t('{{variable}}')` - Double curly braces (Vue/Angular)
- `t('{variable}')` - Single curly braces
- `t('key1 + key2')` - Concatenation expressions
- `t('getKey()')` - Function call expressions
- `t('keys[index]')` - Array/object access
- `t('condition ? "key1" : "key2"')` - Ternary operators
- `t('key1 || key2')` - Logical operators
- `t('getUserName')` - Variable-like patterns
- `t('isActive')` - Boolean-like patterns
- `t('camelCaseVar')` - CamelCase variables
- `t('CONSTANT_VAR')` - Constants
- `t('_privateVar')` - Private variables

### Pattern Improvements
The tool has been enhanced to prevent false positives by:
1. **Boundary detection**: Only matches `t(` when preceded by non-identifier characters
2. **Method call exclusion**: Ignores `object.t()` patterns
3. **Dynamic key detection**: Filters out template literals, expressions, and variables
4. **Context awareness**: Considers the surrounding code context
5. **Comprehensive filtering**: Supports all popular dynamic key patterns across frameworks

## Best Practices

1. **Run regularly**: Include in your development workflow
2. **Fix immediately**: Address missing translations as soon as they're found
3. **Use in CI/CD**: Prevent missing translations from reaching production
4. **Document patterns**: Maintain consistent translation key naming
5. **Review suggestions**: Use the tool's suggestions to improve your translation organization
6. **Verify results**: Check flagged items to ensure they're actual translation calls

# Smart Translation Deleter

The `delete_translation_smart` tool intelligently deletes single or multiple translation keys with comprehensive dependency checking, conflict detection, and safe deletion practices.

## Overview

This tool provides smart deletion capabilities for translation keys, supporting both single and bulk operations. It includes dependency analysis, dry run mode, language-specific deletion, and automatic file cleanup.

## Features

- ✅ **Single & Bulk Operations**: Delete individual keys or multiple keys in batches
- 🔍 **Dependency Analysis**: Detects child keys and parent relationships
- 🛡️ **Safety Checks**: Warns about potential issues before deletion
- 🏃 **Dry Run Mode**: Preview what would be deleted without actual deletion
- 🌐 **Language-Specific**: Delete from specific languages or all languages
- 📁 **File Synchronization**: Automatically updates translation files
- 🧹 **Cleanup**: Removes empty parent objects after deletion
- ⚡ **Performance**: Batch processing for large deletion operations
- 🔄 **Rollback**: Atomic operations with error handling

## Usage

### Basic Usage

```typescript
// Delete a key from all languages
const result = await mcpClient.callTool('delete_translation_smart', {
  keyPath: 'common.buttons.save'
});
```

### Advanced Usage

```typescript
// Delete with all options
const result = await mcpClient.callTool('delete_translation_smart', {
  keyPath: 'common.buttons.save',
  languages: ['fr', 'de'],          // Only delete from specific languages
  dryRun: false,                    // Actually perform deletion
  checkDependencies: true,          // Check for child keys
  writeToFiles: true,               // Update actual files
  force: false                      // Don't force if warnings exist
});
```

### Bulk Operations

```typescript
// Bulk delete multiple keys
const result = await mcpClient.callTool('delete_translation_smart', {
  deletions: [
    { keyPath: 'common.buttons.save' },
    { keyPath: 'common.buttons.cancel', languages: ['fr'] },
    { keyPath: 'auth.login.title' }
  ],
  skipOnError: true,                // Continue on individual failures
  batchSize: 50                     // Process in batches
});
```

## Parameters

### Single Deletion Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `keyPath` | string | - | Translation key path to delete |
| `languages` | string[] | - | Specific languages to delete from (if not provided, deletes from all) |

### Bulk Deletion Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `deletions` | array | - | Array of deletion operations |
| `deletions[].keyPath` | string | - | Key path to delete |
| `deletions[].languages` | string[] | - | Languages to delete from |

### Common Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `dryRun` | boolean | `false` | Preview without actual deletion |
| `checkDependencies` | boolean | `true` | Check for child keys and dependencies |
| `writeToFiles` | boolean | `true` | Write changes to actual files |
| `force` | boolean | `false` | Force deletion even if warnings exist |

### Bulk-Specific Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `skipOnError` | boolean | `true` | Skip individual entries on error |
| `batchSize` | number | `50` | Process deletions in batches |

## Response Format

### Single Deletion Response

```typescript
{
  "success": boolean,
  "keyPath": string,
  "deletedLanguages": string[],
  "remainingLanguages": string[],
  "completelyRemoved": boolean,
  "fileWriteResults": {
    "[language]": {
      "success": boolean,
      "error": string
    }
  },
  "warnings": string[],
  "skipReason": string,
  "dryRun": boolean
}
```

### Bulk Deletion Response

```typescript
{
  "summary": {
    "success": boolean,
    "total": number,
    "processed": number,
    "successful": number,
    "skipped": number,
    "failed": number,
    "errors": number,
    "performance": {
      "batchSize": number,
      "totalBatches": number
    }
  },
  "results": [...],  // Individual deletion results
  "errors": [...],   // Error messages
  "dryRun": boolean
}
```

## Examples

### Example 1: Simple Key Deletion

```bash
# Delete a key from all languages
delete_translation_smart --keyPath="common.buttons.save"
```

**Response:**
```json
{
  "success": true,
  "keyPath": "common.buttons.save",
  "deletedLanguages": ["en", "es", "fr"],
  "remainingLanguages": [],
  "completelyRemoved": true,
  "warnings": []
}
```

### Example 2: Language-Specific Deletion

```bash
# Delete from specific languages only
delete_translation_smart --keyPath="common.buttons.save" --languages=["fr","de"]
```

### Example 3: Dry Run with Dependency Checking

```bash
# Preview deletion with dependency analysis
delete_translation_smart --keyPath="common" --dryRun=true --checkDependencies=true
```

**Response:**
```json
{
  "success": true,
  "keyPath": "common",
  "deletedLanguages": ["en", "es"],
  "warnings": [
    "Deleting this key will affect 5 child key(s): common.buttons.save, common.buttons.cancel, ..."
  ],
  "dryRun": true
}
```

### Example 4: Bulk Deletion

```bash
# Bulk delete multiple keys
delete_translation_smart --deletions='[
  {"keyPath": "common.buttons.save"},
  {"keyPath": "common.buttons.cancel", "languages": ["fr"]},
  {"keyPath": "auth.login.title"}
]'
```

### Example 5: Forced Deletion

```bash
# Force deletion despite warnings
delete_translation_smart --keyPath="common" --force=true --checkDependencies=true
```

## Safety Features

### Dependency Analysis

The tool automatically analyzes dependencies:

- **Child Keys**: Warns if deleting a key that has nested children
- **Parent Keys**: Identifies if the key is part of a nested structure
- **Sibling Keys**: Shows related keys at the same level

### Warning System

Common warnings include:

- Child key dependencies
- Base language deletion while keeping other languages
- Nested structure implications

### Dry Run Mode

Use `dryRun: true` to preview what would happen:

```typescript
const preview = await mcpClient.callTool('delete_translation_smart', {
  keyPath: 'common.buttons',
  dryRun: true,
  checkDependencies: true
});
```

## File Management

### Automatic Cleanup

The tool automatically:

- Removes empty parent objects after deletion
- Cleans up nested structures
- Maintains valid JSON formatting

### Example Cleanup

Before deletion:
```json
{
  "common": {
    "buttons": {
      "save": "Save"
    }
  }
}
```

After deleting `common.buttons.save`:
```json
{}
```

## Error Handling

### Individual Errors

- Non-existent keys are skipped with appropriate messages
- Invalid language codes are handled gracefully
- File write errors are reported per language

### Bulk Operation Errors

- `skipOnError: true` continues processing despite individual failures
- Detailed error reporting for each failed operation
- Summary statistics for overall operation success

## Best Practices

1. **Use Dry Run First**: Always preview deletions with `dryRun: true`
2. **Check Dependencies**: Enable `checkDependencies` for important keys
3. **Language-Specific**: Use language-specific deletion when appropriate
4. **Batch Operations**: Use bulk deletion for multiple keys
5. **Force Carefully**: Only use `force: true` when you understand the implications

## Integration Examples

### Pre-deletion Check

```typescript
// Check what would be deleted
const preview = await mcpClient.callTool('delete_translation_smart', {
  keyPath: 'common.buttons',
  dryRun: true,
  checkDependencies: true
});

if (preview.warnings.length > 0) {
  console.log('Warnings:', preview.warnings);
  // Ask user for confirmation
}
```

### Cleanup Unused Keys

```typescript
// Delete multiple unused keys
const unusedKeys = ['old.feature.title', 'deprecated.message', 'temp.debug'];
const deletions = unusedKeys.map(keyPath => ({ keyPath }));

await mcpClient.callTool('delete_translation_smart', {
  deletions,
  checkDependencies: true,
  skipOnError: true
});
```

## Related Tools

- `add_translation_smart`: Add new translations
- `update_translation`: Update existing translations
- `check_translation_integrity`: Validate file integrity
- `search_translation`: Find translations before deletion

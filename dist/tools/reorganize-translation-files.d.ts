/**
 * Translation file reorganization tool
 * Reorganizes translation files to match the base language structure and key order
 */
import { ServerConfig } from '../types/translation.js';
/**
 * Result of reorganization operation
 */
export interface ReorganizationResult {
    /** Overall success status */
    success: boolean;
    /** Base language used as source of truth */
    baseLanguage: string;
    /** Total files processed */
    totalFiles: number;
    /** Summary statistics */
    summary: {
        filesReorganized: number;
        keysAdded: number;
        keysRemoved: number;
        filesSkipped: number;
        errors: number;
    };
    /** Per-file results */
    fileResults: Record<string, FileReorganizationResult>;
    /** Any errors encountered */
    errors: string[];
    /** Recommendations */
    recommendations: string[];
}
/**
 * Result for individual file reorganization
 */
export interface FileReorganizationResult {
    /** Language code */
    language: string;
    /** File path */
    filePath: string;
    /** Whether file was reorganized */
    reorganized: boolean;
    /** Reason if not reorganized */
    skipReason?: string;
    /** Changes made */
    changes: {
        keysAdded: number;
        keysRemoved: number;
        keysReordered: boolean;
    };
    /** Any errors */
    error?: string;
}
/**
 * Setup the reorganize translation files tool
 */
export declare function setupReorganizeTranslationFilesTool(server: any, config: Required<ServerConfig>): void;
/**
 * Translation file reorganizer implementation
 */
export declare class TranslationFileReorganizer {
    private readonly config;
    private readonly integrityChecker;
    constructor(config: Required<ServerConfig>);
    /**
     * Reorganize all translation files to match base language structure
     */
    reorganizeFiles(options: {
        baseLanguage: string;
        dryRun: boolean;
        backupFiles: boolean;
        preserveComments: boolean;
        includeDetails: boolean;
        onlyReorganizeFiles?: string[];
    }): Promise<ReorganizationResult>;
    /**
     * Reorganize a single translation file
     */
    private reorganizeSingleFile;
    /**
     * Create reorganized structure based on base language template
     */
    private createReorganizedStructure;
    /**
     * Recursively build structure maintaining base language key order
     */
    private buildStructureRecursively;
    /**
     * Check if key order has changed between two objects
     */
    private hasKeyOrderChanged;
    /**
     * Create backup file with timestamp
     */
    private createBackupFile;
    /**
     * Load and parse a translation file
     */
    private loadTranslationFile;
    /**
     * Get all translation files in the directory
     */
    private getTranslationFiles;
    /**
     * Extract language code from filename
     */
    private extractLanguageFromFilename;
    /**
     * Generate recommendations based on reorganization results
     */
    private generateRecommendations;
}
//# sourceMappingURL=reorganize-translation-files.d.ts.map
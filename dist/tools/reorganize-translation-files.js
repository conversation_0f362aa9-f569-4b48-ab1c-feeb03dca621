/**
 * Translation file reorganization tool
 * Reorganizes translation files to match the base language structure and key order
 */
import { z } from 'zod';
import { promises as fs } from 'fs';
import { join, extname } from 'path';
import { TranslationIntegrityChecker } from './check-translation-integrity.js';
import { JsonOperations } from '../utils/json-operations.js';
import { ObjectManipulator } from '../utils/object-manipulator.js';
/**
 * Setup the reorganize translation files tool
 */
export function setupReorganizeTranslationFilesTool(server, config) {
    server.tool('reorganize_translation_files', 'Reorganize translation files to match base language structure and key order', {
        baseLanguage: z.string().optional().describe('Base language to use as source of truth (default: en)'),
        dryRun: z.boolean().default(false).describe('Preview changes without writing files'),
        backupFiles: z.boolean().default(true).describe('Create backup files before reorganizing'),
        preserveComments: z.boolean().default(true).describe('Preserve JSON comments if present'),
        includeDetails: z.boolean().default(true).describe('Include detailed results for each file'),
        onlyReorganizeFiles: z.array(z.string()).optional().describe('Only reorganize specific language files (e.g., ["fr", "de"])')
    }, async ({ baseLanguage, dryRun, backupFiles, preserveComments, includeDetails, onlyReorganizeFiles }) => {
        try {
            const reorganizer = new TranslationFileReorganizer(config);
            const result = await reorganizer.reorganizeFiles({
                baseLanguage: baseLanguage || config.baseLanguage,
                dryRun,
                backupFiles,
                preserveComments,
                includeDetails,
                onlyReorganizeFiles
            });
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify(result, null, 2)
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `Error reorganizing translation files: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }]
            };
        }
    });
}
/**
 * Translation file reorganizer implementation
 */
export class TranslationFileReorganizer {
    config;
    integrityChecker;
    constructor(config) {
        this.config = config;
        this.integrityChecker = new TranslationIntegrityChecker(config);
    }
    /**
     * Reorganize all translation files to match base language structure
     */
    async reorganizeFiles(options) {
        const { baseLanguage, dryRun, backupFiles, preserveComments, includeDetails, onlyReorganizeFiles } = options;
        // First, check integrity to understand what needs to be fixed
        const integrityResult = await this.integrityChecker.checkIntegrity({
            baseLanguage,
            includeDetails: true,
            onlyShowIssues: false,
            checkTypes: true
        });
        if (!integrityResult.isValid && Object.keys(integrityResult.fileResults).length === 0) {
            throw new Error(`No translation files found or base language file ${baseLanguage}.json is invalid`);
        }
        // Load base language structure
        const baseFilePath = join(this.config.translationDir, `${baseLanguage}.json`);
        const baseData = await this.loadTranslationFile(baseFilePath);
        if (!baseData.validJson || !baseData.data) {
            throw new Error(`Base language file ${baseLanguage}.json is invalid or missing`);
        }
        // Initialize result
        const result = {
            success: true,
            baseLanguage,
            totalFiles: 0,
            summary: {
                filesReorganized: 0,
                keysAdded: 0,
                keysRemoved: 0,
                filesSkipped: 0,
                errors: 0
            },
            fileResults: {},
            errors: [],
            recommendations: []
        };
        // Get all translation files
        const translationFiles = await this.getTranslationFiles();
        result.totalFiles = translationFiles.length;
        // Process each file
        for (const file of translationFiles) {
            const language = this.extractLanguageFromFilename(file);
            // Skip base language
            if (language === baseLanguage) {
                continue;
            }
            // Skip if only reorganizing specific files
            if (onlyReorganizeFiles && !onlyReorganizeFiles.includes(language)) {
                continue;
            }
            try {
                const fileResult = await this.reorganizeSingleFile(file, language, baseData.data, { dryRun, backupFiles, preserveComments });
                result.fileResults[language] = fileResult;
                if (fileResult.reorganized) {
                    result.summary.filesReorganized++;
                    result.summary.keysAdded += fileResult.changes.keysAdded;
                    result.summary.keysRemoved += fileResult.changes.keysRemoved;
                }
                else {
                    result.summary.filesSkipped++;
                }
                if (fileResult.error) {
                    result.summary.errors++;
                    result.errors.push(`${language}: ${fileResult.error}`);
                }
            }
            catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                result.summary.errors++;
                result.errors.push(`${language}: ${errorMsg}`);
                result.fileResults[language] = {
                    language,
                    filePath: file,
                    reorganized: false,
                    skipReason: 'Error during processing',
                    changes: { keysAdded: 0, keysRemoved: 0, keysReordered: false },
                    error: errorMsg
                };
            }
        }
        // Generate recommendations
        result.recommendations = this.generateRecommendations(result, dryRun);
        // Update overall success status
        result.success = result.summary.errors === 0;
        return result;
    }
    /**
     * Reorganize a single translation file
     */
    async reorganizeSingleFile(filePath, language, baseStructure, options) {
        const result = {
            language,
            filePath,
            reorganized: false,
            changes: { keysAdded: 0, keysRemoved: 0, keysReordered: false }
        };
        // Load current file
        const currentData = await this.loadTranslationFile(filePath);
        if (!currentData.validJson) {
            result.skipReason = 'Invalid JSON syntax';
            result.error = currentData.parseError;
            return result;
        }
        const currentTranslations = currentData.data || {};
        // Create reorganized structure based on base language
        const reorganizedStructure = this.createReorganizedStructure(baseStructure, currentTranslations);
        // Calculate changes
        const currentPaths = ObjectManipulator.getAllPaths(currentTranslations);
        const newPaths = ObjectManipulator.getAllPaths(reorganizedStructure);
        result.changes.keysAdded = newPaths.filter(path => !currentPaths.includes(path)).length;
        result.changes.keysRemoved = currentPaths.filter(path => !newPaths.includes(path)).length;
        result.changes.keysReordered = this.hasKeyOrderChanged(currentTranslations, reorganizedStructure);
        // Check if any changes are needed
        const hasChanges = result.changes.keysAdded > 0 ||
            result.changes.keysRemoved > 0 ||
            result.changes.keysReordered;
        if (!hasChanges) {
            result.skipReason = 'No changes needed';
            return result;
        }
        // If not dry run, write the reorganized file
        if (!options.dryRun) {
            try {
                // Create backup if requested
                if (options.backupFiles) {
                    await this.createBackupFile(filePath);
                }
                // Write reorganized file
                await JsonOperations.writeFile(filePath, reorganizedStructure, 2);
                result.reorganized = true;
            }
            catch (error) {
                result.error = error instanceof Error ? error.message : 'Unknown error writing file';
                return result;
            }
        }
        else {
            // For dry run, mark as reorganized to show what would happen
            result.reorganized = true;
        }
        return result;
    }
    /**
     * Create reorganized structure based on base language template
     */
    createReorganizedStructure(baseStructure, currentTranslations) {
        const reorganized = {};
        // Recursively build structure following base language order
        this.buildStructureRecursively(baseStructure, currentTranslations, reorganized);
        return reorganized;
    }
    /**
     * Recursively build structure maintaining base language key order
     */
    buildStructureRecursively(baseObj, currentObj, targetObj, currentPath = '') {
        if (!baseObj || typeof baseObj !== 'object' || Array.isArray(baseObj)) {
            return;
        }
        // Process keys in the order they appear in base language
        for (const [key, baseValue] of Object.entries(baseObj)) {
            const keyPath = currentPath ? `${currentPath}.${key}` : key;
            if (baseValue && typeof baseValue === 'object' && !Array.isArray(baseValue)) {
                // Nested object - recurse
                targetObj[key] = {};
                this.buildStructureRecursively(baseValue, currentObj?.[key] || {}, targetObj[key], keyPath);
            }
            else {
                // Leaf node - use current translation if available, otherwise use base value as placeholder
                if (currentObj && typeof currentObj === 'object' && key in currentObj) {
                    targetObj[key] = currentObj[key];
                }
                else {
                    // Use base value as placeholder (this will be a missing translation)
                    targetObj[key] = baseValue;
                }
            }
        }
    }
    /**
     * Check if key order has changed between two objects
     */
    hasKeyOrderChanged(obj1, obj2) {
        if (!obj1 || !obj2 || typeof obj1 !== 'object' || typeof obj2 !== 'object') {
            return false;
        }
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        // If different number of keys, order has changed
        if (keys1.length !== keys2.length) {
            return true;
        }
        // Check if keys are in same order
        for (let i = 0; i < keys1.length; i++) {
            if (keys1[i] !== keys2[i]) {
                return true;
            }
        }
        // Recursively check nested objects
        for (const key of keys1) {
            if (key in obj2) {
                const val1 = obj1[key];
                const val2 = obj2[key];
                if (val1 && val2 && typeof val1 === 'object' && typeof val2 === 'object' &&
                    !Array.isArray(val1) && !Array.isArray(val2)) {
                    if (this.hasKeyOrderChanged(val1, val2)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    /**
     * Create backup file with timestamp
     */
    async createBackupFile(filePath) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = `${filePath}.backup-${timestamp}`;
        try {
            await fs.copyFile(filePath, backupPath);
        }
        catch (error) {
            throw new Error(`Failed to create backup file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Load and parse a translation file
     */
    async loadTranslationFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            const data = JSON.parse(content);
            return { validJson: true, data };
        }
        catch (error) {
            return {
                validJson: false,
                parseError: error instanceof Error ? error.message : 'Unknown parse error'
            };
        }
    }
    /**
     * Get all translation files in the directory
     */
    async getTranslationFiles() {
        try {
            const files = await fs.readdir(this.config.translationDir);
            return files
                .filter(file => extname(file) === '.json')
                .map(file => join(this.config.translationDir, file));
        }
        catch (error) {
            throw new Error(`Cannot read translation directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Extract language code from filename
     */
    extractLanguageFromFilename(filePath) {
        const filename = filePath.split('/').pop() || '';
        return filename.replace('.json', '');
    }
    /**
     * Generate recommendations based on reorganization results
     */
    generateRecommendations(result, dryRun) {
        const recommendations = [];
        if (result.summary.errors > 0) {
            recommendations.push(`🚨 ${result.summary.errors} file${result.summary.errors > 1 ? 's' : ''} had errors during reorganization`);
        }
        if (result.summary.filesReorganized > 0) {
            if (dryRun) {
                recommendations.push(`📋 ${result.summary.filesReorganized} file${result.summary.filesReorganized > 1 ? 's' : ''} would be reorganized (dry run mode)`);
            }
            else {
                recommendations.push(`✅ Successfully reorganized ${result.summary.filesReorganized} file${result.summary.filesReorganized > 1 ? 's' : ''}`);
            }
        }
        if (result.summary.keysAdded > 0) {
            recommendations.push(`📝 ${result.summary.keysAdded} missing key${result.summary.keysAdded > 1 ? 's' : ''} ${dryRun ? 'would be' : 'were'} added with base language values as placeholders`);
        }
        if (result.summary.keysRemoved > 0) {
            recommendations.push(`🗑️ ${result.summary.keysRemoved} extra key${result.summary.keysRemoved > 1 ? 's' : ''} ${dryRun ? 'would be' : 'were'} removed`);
        }
        if (result.summary.filesSkipped > 0) {
            recommendations.push(`⏭️ ${result.summary.filesSkipped} file${result.summary.filesSkipped > 1 ? 's' : ''} skipped (no changes needed or errors)`);
        }
        if (result.summary.filesReorganized === 0 && result.summary.errors === 0) {
            recommendations.push('✨ All translation files are already properly organized');
        }
        if (dryRun && result.summary.filesReorganized > 0) {
            recommendations.push('💡 Run without dryRun=true to apply these changes');
        }
        if (!dryRun && result.summary.filesReorganized > 0) {
            recommendations.push('🔄 Consider running the check_translation_integrity tool to verify the reorganization');
        }
        return recommendations;
    }
}
//# sourceMappingURL=reorganize-translation-files.js.map
import { TranslationIndex } from '../core/translation-index.js';
import { SupportedFramework } from '../types/translation.js';
/**
 * Result of missing translation search
 */
interface MissingTranslationResult {
    /** Total files scanned */
    totalFilesScanned: number;
    /** Total translation keys found in code */
    totalKeysFound: number;
    /** Number of missing translation keys */
    missingKeysCount: number;
    /** Missing translation keys with their locations */
    missingKeys: MissingTranslationKey[];
    /** Summary by file */
    filesSummary: FileSummary[];
    /** Summary by framework */
    frameworkSummary: Record<string, number>;
    /** Scan configuration used */
    scanConfig: {
        srcDir: string;
        frameworks: SupportedFramework[];
        excludePatterns: string[];
        fileExtensions: string[];
    };
}
/**
 * Missing translation key with location information
 */
interface MissingTranslationKey {
    /** The missing translation key */
    key: string;
    /** Files where this key is used */
    usages: KeyUsageLocation[];
    /** Suggested action */
    suggestion?: string;
}
/**
 * Location where a translation key is used
 */
interface KeyUsageLocation {
    /** File path relative to srcDir */
    filePath: string;
    /** Line number in file */
    line: number;
    /** Column number in file */
    column: number;
    /** Pattern that matched */
    pattern: string;
    /** Detected framework */
    framework?: SupportedFramework;
}
/**
 * Summary for a single file
 */
interface FileSummary {
    /** File path relative to srcDir */
    filePath: string;
    /** Detected framework */
    framework?: SupportedFramework;
    /** Total translation keys found */
    totalKeys: number;
    /** Missing translation keys */
    missingKeys: number;
    /** List of missing key names */
    missingKeyNames: string[];
}
/**
 * Setup the search missing translations tool
 */
export declare function setupSearchMissingTranslationsTool(server: any, index: TranslationIndex, config: any): void;
/**
 * Scanner for missing translation keys
 */
export declare class MissingTranslationScanner {
    private readonly index;
    private readonly config;
    constructor(index: TranslationIndex, config: any);
    /**
     * Resolve source directory relative to project root or translation directory
     */
    resolveSrcDir(srcDir: string): string;
    /**
     * Scan for missing translation keys across the codebase
     */
    scanForMissingTranslations(options: {
        srcDir: string;
        frameworks: SupportedFramework[];
        excludePatterns: string[];
        fileExtensions: string[];
        includeDetails: boolean;
        groupByKey: boolean;
        maxDepth: number;
    }): Promise<MissingTranslationResult>;
    /**
     * Get all source files to scan
     */
    private getSourceFiles;
    /**
     * Generate suggestion for missing translation key
     */
    private generateSuggestion;
    /**
     * Flatten missing keys for non-grouped output
     */
    private flattenMissingKeys;
}
export {};
//# sourceMappingURL=search-missing-translations.d.ts.map
/**
 * Search for missing translation keys across the entire codebase
 */
import { z } from 'zod';
import { readdir, stat } from 'fs/promises';
import { join, extname, resolve, isAbsolute } from 'path';
import { CodeAnalyzer } from '../core/code-analyzer.js';
/**
 * Setup the search missing translations tool
 */
export function setupSearchMissingTranslationsTool(server, index, config) {
    server.tool('search_missing_translations', 'Search for translation keys that are used in code but missing from translation files. Supports React, Vue, Svelte, and Angular frameworks. ', {
        srcDir: z.string().optional().describe('Source directory to scan (defaults to config srcDir)'),
        frameworks: z.array(z.enum(['react', 'vue', 'svelte', 'angular'])).optional().describe('Frameworks to analyze. Supported: react, vue, svelte, angular. '),
        excludePatterns: z.array(z.string()).optional().describe('File/directory patterns to exclude'),
        fileExtensions: z.array(z.string()).optional().describe('File extensions to scan'),
        includeDetails: z.boolean().default(true).describe('Include detailed location information'),
        groupByKey: z.boolean().default(true).describe('Group results by translation key'),
        maxDepth: z.number().min(1).max(10).default(5).describe('Maximum directory depth to scan')
    }, async ({ srcDir, frameworks, excludePatterns, fileExtensions, includeDetails, groupByKey, maxDepth }) => {
        try {
            // Validate frameworks and provide helpful messages
            const supportedFrameworks = ['react', 'vue', 'svelte', 'angular'];
            const requestedFrameworks = frameworks || ['react', 'vue', 'svelte', 'angular'];
            const unsupportedFrameworks = requestedFrameworks.filter(f => !supportedFrameworks.includes(f));
            if (unsupportedFrameworks.length > 0) {
                const frameworkMappings = {
                    'nuxt': 'vue (Nuxt uses Vue files)',
                    'next': 'react (Next.js uses React)',
                    'nextjs': 'react (Next.js uses React)',
                    'gatsby': 'react (Gatsby uses React)',
                    'vite': 'Use the underlying framework (react/vue/svelte)',
                    'webpack': 'Use the underlying framework (react/vue/svelte)'
                };
                const suggestions = unsupportedFrameworks.map(f => {
                    const suggestion = frameworkMappings[f.toLowerCase()];
                    return suggestion ? `"${f}" -> use "${suggestion}"` : `"${f}" is not supported`;
                }).join(', ');
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                error: 'Unsupported framework(s) detected',
                                unsupportedFrameworks,
                                supportedFrameworks,
                                suggestions,
                                message: `Please use one of the supported frameworks: ${supportedFrameworks.join(', ')}`
                            }, null, 2)
                        }]
                };
            }
            const scanner = new MissingTranslationScanner(index, config);
            // Resolve source directory relative to project root or translation directory
            const resolvedSrcDir = scanner.resolveSrcDir(srcDir || config.srcDir || './src');
            const result = await scanner.scanForMissingTranslations({
                srcDir: resolvedSrcDir,
                frameworks: requestedFrameworks,
                excludePatterns: excludePatterns || ['node_modules', 'dist', 'build', '.git'],
                fileExtensions: fileExtensions || ['.ts', '.tsx', '.js', '.jsx', '.vue', '.svelte', '.html'],
                includeDetails,
                groupByKey,
                maxDepth
            });
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify(result, null, 2)
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `Error searching for missing translations: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }]
            };
        }
    });
}
/**
 * Scanner for missing translation keys
 */
export class MissingTranslationScanner {
    index;
    config;
    constructor(index, config) {
        this.index = index;
        this.config = config;
    }
    /**
     * Resolve source directory relative to project root or translation directory
     */
    resolveSrcDir(srcDir) {
        // If it's already an absolute path, use it as-is
        if (isAbsolute(srcDir)) {
            if (this.config.debug) {
                console.log(`🔍 Using absolute srcDir: ${srcDir}`);
            }
            return srcDir;
        }
        let resolvedPath;
        // Try to resolve relative to project root first
        if (this.config.projectRoot) {
            resolvedPath = resolve(this.config.projectRoot, srcDir);
            if (this.config.debug) {
                console.log(`🔍 Resolved srcDir relative to projectRoot (${this.config.projectRoot}): ${srcDir} -> ${resolvedPath}`);
            }
            return resolvedPath;
        }
        // Fallback: resolve relative to translation directory parent
        if (this.config.translationDir) {
            const translationDirParent = resolve(this.config.translationDir, '..');
            resolvedPath = resolve(translationDirParent, srcDir);
            if (this.config.debug) {
                console.log(`🔍 Resolved srcDir relative to translation dir parent (${translationDirParent}): ${srcDir} -> ${resolvedPath}`);
            }
            return resolvedPath;
        }
        // Last resort: resolve relative to current working directory
        resolvedPath = resolve(process.cwd(), srcDir);
        if (this.config.debug) {
            console.log(`🔍 Resolved srcDir relative to cwd (${process.cwd()}): ${srcDir} -> ${resolvedPath}`);
        }
        return resolvedPath;
    }
    /**
     * Scan for missing translation keys across the codebase
     */
    async scanForMissingTranslations(options) {
        const { srcDir, frameworks, excludePatterns, fileExtensions, includeDetails, groupByKey, maxDepth } = options;
        // Get all source files
        const sourceFiles = await this.getSourceFiles(srcDir, fileExtensions, excludePatterns, maxDepth);
        if (sourceFiles.length === 0) {
            // Provide more detailed error information
            const debugInfo = this.config.debug ? [
                `  - Resolved path: ${srcDir}`,
                `  - Project root: ${this.config.projectRoot || 'not set'}`,
                `  - Translation dir: ${this.config.translationDir || 'not set'}`,
                `  - Current working dir: ${process.cwd()}`,
                `  - Exclude patterns: ${excludePatterns.join(', ')}`,
                `  - Max depth: ${maxDepth}`
            ].join('\n') : '';
            throw new Error(`No source files found in "${srcDir}" with extensions: ${fileExtensions.join(', ')}\n${debugInfo}`);
        }
        // Analyze each file for translation usage
        const analyzer = new CodeAnalyzer(frameworks);
        const allUsages = [];
        const filesSummary = [];
        const frameworkCounts = {};
        for (const filePath of sourceFiles) {
            try {
                const analysisResult = await analyzer.analyzeFile(filePath, {
                    extractHardcoded: false,
                    findUsage: true,
                    translationIndex: this.index,
                    minStringLength: 1,
                    excludePatterns: []
                });
                // Count framework usage
                if (analysisResult.detectedFramework) {
                    frameworkCounts[analysisResult.detectedFramework] =
                        (frameworkCounts[analysisResult.detectedFramework] || 0) + 1;
                }
                // Filter for missing translations only and add file context
                const missingUsages = analysisResult.translationUsage
                    .filter(usage => !usage.exists)
                    .map(usage => ({
                    ...usage,
                    filePath,
                    framework: analysisResult.detectedFramework
                }));
                allUsages.push(...missingUsages);
                // Create file summary
                const fileSummary = {
                    filePath: filePath.replace(srcDir + '/', ''),
                    framework: analysisResult.detectedFramework,
                    totalKeys: analysisResult.translationUsage.length,
                    missingKeys: missingUsages.length,
                    missingKeyNames: missingUsages.map(u => u.keyPath)
                };
                filesSummary.push(fileSummary);
            }
            catch (error) {
                if (this.config.debug) {
                    console.warn(`Failed to analyze file ${filePath}:`, error);
                }
                // Continue with other files
            }
        }
        // Group missing keys
        const missingKeysMap = new Map();
        for (const usage of allUsages) {
            if (!missingKeysMap.has(usage.keyPath)) {
                missingKeysMap.set(usage.keyPath, []);
            }
            missingKeysMap.get(usage.keyPath).push({
                filePath: usage.filePath?.replace(srcDir + '/', '') || 'unknown',
                line: usage.line,
                column: usage.column,
                pattern: usage.pattern,
                framework: usage.framework
            });
        }
        // Create missing keys array
        const missingKeys = Array.from(missingKeysMap.entries()).map(([key, usages]) => ({
            key,
            usages: includeDetails ? usages : usages.slice(0, 1), // Limit details if requested
            suggestion: this.generateSuggestion(key, usages)
        }));
        // Sort by usage frequency (most used missing keys first)
        missingKeys.sort((a, b) => b.usages.length - a.usages.length);
        return {
            totalFilesScanned: sourceFiles.length,
            totalKeysFound: allUsages.length,
            missingKeysCount: missingKeys.length,
            missingKeys: groupByKey ? missingKeys : this.flattenMissingKeys(missingKeys),
            filesSummary: filesSummary.filter(f => f.missingKeys > 0), // Only files with issues
            frameworkSummary: frameworkCounts,
            scanConfig: {
                srcDir,
                frameworks,
                excludePatterns,
                fileExtensions
            }
        };
    }
    /**
     * Get all source files to scan
     */
    async getSourceFiles(srcDir, extensions, excludePatterns, maxDepth) {
        const files = [];
        const excludeRegexes = excludePatterns.map(pattern => new RegExp(pattern));
        const scanDirectory = async (dir, currentDepth) => {
            if (currentDepth > maxDepth)
                return;
            try {
                const entries = await readdir(dir);
                for (const entry of entries) {
                    const fullPath = join(dir, entry);
                    // Check if path should be excluded
                    if (excludeRegexes.some(regex => regex.test(fullPath))) {
                        continue;
                    }
                    const stats = await stat(fullPath);
                    if (stats.isDirectory()) {
                        await scanDirectory(fullPath, currentDepth + 1);
                    }
                    else if (stats.isFile()) {
                        const ext = extname(entry);
                        if (extensions.includes(ext)) {
                            files.push(fullPath);
                        }
                    }
                }
            }
            catch (error) {
                if (this.config.debug) {
                    console.warn(`Failed to scan directory ${dir}:`, error);
                }
            }
        };
        await scanDirectory(srcDir, 0);
        return files;
    }
    /**
     * Generate suggestion for missing translation key
     */
    generateSuggestion(key, usages) {
        const frameworks = [...new Set(usages.map(u => u.framework).filter(Boolean))];
        const uniqueFiles = [...new Set(usages.map(u => u.filePath))];
        const fileCount = uniqueFiles.length;
        if (fileCount === 1) {
            return `Add translation for "${key}" - used in 1 file`;
        }
        else {
            return `Add translation for "${key}" - used in ${fileCount} files${frameworks.length > 0 ? ` (${frameworks.join(', ')})` : ''}`;
        }
    }
    /**
     * Flatten missing keys for non-grouped output
     */
    flattenMissingKeys(missingKeys) {
        const flattened = [];
        for (const missingKey of missingKeys) {
            for (const usage of missingKey.usages) {
                flattened.push({
                    key: missingKey.key,
                    filePath: usage.filePath,
                    line: usage.line,
                    column: usage.column,
                    pattern: usage.pattern,
                    framework: usage.framework,
                    suggestion: missingKey.suggestion
                });
            }
        }
        return flattened;
    }
}
//# sourceMappingURL=search-missing-translations.js.map
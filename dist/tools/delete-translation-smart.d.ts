/**
 * Smart translation deletion tool with single and bulk operations
 */
import { TranslationIndex } from '../core/translation-index.js';
import { DeleteTranslationResult, DeleteTranslationInput, ServerConfig } from '../types/translation.js';
/**
 * Setup the smart delete translation tool
 */
export declare function setupDeleteTranslationSmartTool(server: any, index: TranslationIndex, config: Required<ServerConfig>): void;
/**
 * Smart translation deleter implementation
 */
export declare class SmartTranslationDeleter {
    private readonly index;
    private readonly config;
    constructor(index: TranslationIndex, config: Required<ServerConfig>);
    /**
     * Handle single translation deletion
     */
    handleSingleDeletion(deletion: DeleteTranslationInput, options: {
        dryRun: boolean;
        checkDependencies: boolean;
        writeToFiles: boolean;
        force: boolean;
    }): Promise<{
        content: {
            type: string;
            text: string;
        }[];
    }>;
    /**
     * Handle bulk translation deletions
     */
    handleBulkDeletions(deletions: Array<{
        keyPath: string;
        languages?: string[];
    }>, options: {
        dryRun: boolean;
        checkDependencies: boolean;
        writeToFiles: boolean;
        force: boolean;
        skipOnError: boolean;
        batchSize: number;
    }): Promise<{
        content: {
            type: string;
            text: string;
        }[];
    }>;
    /**
     * Process a single deletion operation
     */
    processSingleDeletion(deletion: DeleteTranslationInput, options: {
        dryRun: boolean;
        checkDependencies: boolean;
        writeToFiles: boolean;
        force: boolean;
    }): Promise<DeleteTranslationResult>;
    /**
     * Analyze dependencies for a translation key
     */
    private analyzeDependencies;
    /**
     * Remove a translation from its corresponding file
     */
    private removeTranslationFromFile;
    /**
     * Remove a nested value from an object using dot notation
     */
    private removeNestedValue;
    /**
     * Clean up empty parent objects after deletion
     */
    private cleanupEmptyParents;
}
//# sourceMappingURL=delete-translation-smart.d.ts.map
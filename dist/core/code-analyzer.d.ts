/**
 * Code analysis engine for detecting hardcoded strings and translation usage
 */
import { SupportedFramework, CodeAnalysisResult } from '../types/translation.js';
import { TranslationIndex } from './translation-index.js';
/**
 * Options for code analysis
 */
export interface CodeAnalysisOptions {
    /** Extract hardcoded strings */
    extractHardcoded?: boolean;
    /** Find translation key usage */
    findUsage?: boolean;
    /** Translation index for validation */
    translationIndex?: TranslationIndex;
    /** Minimum string length to consider */
    minStringLength?: number;
    /** Exclude patterns */
    excludePatterns?: RegExp[];
}
/**
 * Code analyzer for detecting translation patterns and hardcoded strings
 */
export declare class CodeAnalyzer {
    private frameworks;
    constructor(frameworks?: SupportedFramework[]);
    /**
     * Analyze a single file for translation patterns
     */
    analyzeFile(filePath: string, options?: CodeAnalysisOptions): Promise<CodeAnalysisResult>;
    /**
     * Detect framework based on file extension and content
     */
    private detectFramework;
    /**
     * Extract hardcoded strings from content
     */
    private extractHardcodedStrings;
    /**
     * Find translation key usage in content
     */
    private findTranslationUsage;
    /**
     * Extract static parts from template literals for analysis
     * For example: "categories.${a.id}" -> ["categories"]
     */
    private extractStaticPartsFromTemplate;
    /**
     * Check if a translation key is dynamic (contains variables or expressions)
     */
    private isDynamicKey;
    /**
     * Generate suggestions based on analysis results
     */
    private generateSuggestions;
    /**
     * Calculate confidence score for hardcoded string extraction
     */
    private calculateConfidence;
    /**
     * Generate a suggested translation key
     */
    private generateKey;
}
//# sourceMappingURL=code-analyzer.d.ts.map
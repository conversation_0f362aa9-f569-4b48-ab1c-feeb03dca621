{"version": 3, "file": "code-analyzer.d.ts", "sourceRoot": "", "sources": ["../../src/core/code-analyzer.ts"], "names": [], "mappings": "AAAA;;GAEG;AAIH,OAAO,EACL,kBAAkB,EAClB,kBAAkB,EAInB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAE1D;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC,gCAAgC;IAChC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,iCAAiC;IACjC,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,uCAAuC;IACvC,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC,wCAAwC;IACxC,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,uBAAuB;IACvB,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;CAC5B;AAuHD;;GAEG;AACH,qBAAa,YAAY;IACvB,OAAO,CAAC,UAAU,CAAuB;gBAE7B,UAAU,CAAC,EAAE,kBAAkB,EAAE;IAI7C;;OAEG;IACG,WAAW,CACf,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,mBAAwB,GAChC,OAAO,CAAC,kBAAkB,CAAC;IA6C9B;;OAEG;IACH,OAAO,CAAC,eAAe;IAqCvB;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAkD/B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IA4E5B;;;OAGG;IACH,OAAO,CAAC,8BAA8B;IAsBtC;;OAEG;IACH,OAAO,CAAC,YAAY;IAwEpB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAmC3B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAiB3B;;OAEG;IACH,OAAO,CAAC,WAAW;CAUpB"}
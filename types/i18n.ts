/**
 * Auto-generated translation types
 * Generated at: 2025-06-03T22:49:40.410Z
 * 
 * This file is automatically generated by i18n-mcp.
 * Do not edit manually - changes will be overwritten.
 */

/**
 * All available translation keys
 */
export type TranslationKey =
  | 'auth.login.subtitle'
  | 'auth.login.title'
  | 'common.buttons.cancel'
  | 'common.buttons.submit'
  | 'common.messages.success'
  | 'config.enabled'
  | 'config.maxItems'
  | 'config.metadata'
  | 'config.tags'
  | 'settings.theme';

/**
 * Translation value types
 */
export interface TranslationValues {
  'auth.login.subtitle': string;
  'auth.login.title': string;
  'common.buttons.cancel': string;
  'common.buttons.submit': string;
  'common.messages.success': string;
  'config.enabled': boolean;
  'config.maxItems': number;
  'config.metadata': Record<string, any>;
  'config.tags': (string)[];
  'settings.theme': string;
}

/**
 * I18n namespace with translation utilities
 */
export namespace I18n {
  /** Available languages */
  export type Language = 'en' | 'es' | 'fr';
  
  /** Translation key type */
  export type Key = TranslationKey;
  
  /** Translation value types */
  export type Values = TranslationValues;

  
  /** Translation function type */
  export type TranslateFunction = (key: Key, params?: Record<string, any>) => string;
  
  /** Translation object type */
  export interface TranslationObject {
    [K in Key]: string;
  }
  
  /** Nested translation structure */
  export type NestedTranslations = {
    [key: string]: string | NestedTranslations;
  };
}

/**
 * Helper types for translation functions
 */
export interface TranslationHelpers {
  /** Check if a key exists */
  hasKey(key: string): key is TranslationKey;
  
  /** Get all keys */
  getKeys(): TranslationKey[];
  
  /** Get available languages */
  getLanguages(): I18n.Language[];
  
  /** Validate translation completeness */
  validateCompleteness(language: I18n.Language): {
    missing: TranslationKey[];
    extra: string[];
  };
}
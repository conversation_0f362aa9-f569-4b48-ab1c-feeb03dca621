/**
 * Translation file reorganization tool
 * Reorganizes translation files to match the base language structure and key order
 */

import { z } from 'zod';
import { promises as fs } from 'fs';
import { join, extname } from 'path';
import { 
  ServerConfig,
  TranslationIntegrityResult,
  FileIntegrityResult
} from '../types/translation.js';
import { TranslationIntegrityChecker } from './check-translation-integrity.js';
import { JsonOperations } from '../utils/json-operations.js';
import { ObjectManipulator } from '../utils/object-manipulator.js';

/**
 * Result of reorganization operation
 */
export interface ReorganizationResult {
  /** Overall success status */
  success: boolean;
  /** Base language used as source of truth */
  baseLanguage: string;
  /** Total files processed */
  totalFiles: number;
  /** Summary statistics */
  summary: {
    filesReorganized: number;
    keysAdded: number;
    keysRemoved: number;
    filesSkipped: number;
    errors: number;
  };
  /** Per-file results */
  fileResults: Record<string, FileReorganizationResult>;
  /** Any errors encountered */
  errors: string[];
  /** Recommendations */
  recommendations: string[];
}

/**
 * Result for individual file reorganization
 */
export interface FileReorganizationResult {
  /** Language code */
  language: string;
  /** File path */
  filePath: string;
  /** Whether file was reorganized */
  reorganized: boolean;
  /** Reason if not reorganized */
  skipReason?: string;
  /** Changes made */
  changes: {
    keysAdded: number;
    keysRemoved: number;
    keysReordered: boolean;
  };
  /** Any errors */
  error?: string;
}

/**
 * Setup the reorganize translation files tool
 */
export function setupReorganizeTranslationFilesTool(
  server: any,
  config: Required<ServerConfig>
): void {
  server.tool(
    'reorganize_translation_files',
    'Reorganize translation files to match base language structure and key order',
    {
      baseLanguage: z.string().optional().describe('Base language to use as source of truth (default: en)'),
      dryRun: z.boolean().default(false).describe('Preview changes without writing files'),
      backupFiles: z.boolean().default(true).describe('Create backup files before reorganizing'),
      preserveComments: z.boolean().default(true).describe('Preserve JSON comments if present'),
      includeDetails: z.boolean().default(true).describe('Include detailed results for each file'),
      onlyReorganizeFiles: z.array(z.string()).optional().describe('Only reorganize specific language files (e.g., ["fr", "de"])')
    },
    async ({ 
      baseLanguage, 
      dryRun, 
      backupFiles, 
      preserveComments, 
      includeDetails,
      onlyReorganizeFiles
    }: {
      baseLanguage?: string;
      dryRun: boolean;
      backupFiles: boolean;
      preserveComments: boolean;
      includeDetails: boolean;
      onlyReorganizeFiles?: string[];
    }) => {
      try {
        const reorganizer = new TranslationFileReorganizer(config);
        const result = await reorganizer.reorganizeFiles({
          baseLanguage: baseLanguage || config.baseLanguage,
          dryRun,
          backupFiles,
          preserveComments,
          includeDetails,
          onlyReorganizeFiles
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error reorganizing translation files: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}

/**
 * Translation file reorganizer implementation
 */
export class TranslationFileReorganizer {
  private readonly integrityChecker: TranslationIntegrityChecker;

  constructor(private readonly config: Required<ServerConfig>) {
    this.integrityChecker = new TranslationIntegrityChecker(config);
  }

  /**
   * Reorganize all translation files to match base language structure
   */
  async reorganizeFiles(options: {
    baseLanguage: string;
    dryRun: boolean;
    backupFiles: boolean;
    preserveComments: boolean;
    includeDetails: boolean;
    onlyReorganizeFiles?: string[];
  }): Promise<ReorganizationResult> {
    const { baseLanguage, dryRun, backupFiles, preserveComments, includeDetails, onlyReorganizeFiles } = options;

    // First, check integrity to understand what needs to be fixed
    const integrityResult = await this.integrityChecker.checkIntegrity({
      baseLanguage,
      includeDetails: true,
      onlyShowIssues: false,
      checkTypes: true
    });

    if (!integrityResult.isValid && Object.keys(integrityResult.fileResults).length === 0) {
      throw new Error(`No translation files found or base language file ${baseLanguage}.json is invalid`);
    }

    // Load base language structure
    const baseFilePath = join(this.config.translationDir, `${baseLanguage}.json`);
    const baseData = await this.loadTranslationFile(baseFilePath);
    
    if (!baseData.validJson || !baseData.data) {
      throw new Error(`Base language file ${baseLanguage}.json is invalid or missing`);
    }

    // Initialize result
    const result: ReorganizationResult = {
      success: true,
      baseLanguage,
      totalFiles: 0,
      summary: {
        filesReorganized: 0,
        keysAdded: 0,
        keysRemoved: 0,
        filesSkipped: 0,
        errors: 0
      },
      fileResults: {},
      errors: [],
      recommendations: []
    };

    // Get all translation files
    const translationFiles = await this.getTranslationFiles();
    result.totalFiles = translationFiles.length;

    // Process each file
    for (const file of translationFiles) {
      const language = this.extractLanguageFromFilename(file);
      
      // Skip base language
      if (language === baseLanguage) {
        continue;
      }

      // Skip if only reorganizing specific files
      if (onlyReorganizeFiles && !onlyReorganizeFiles.includes(language)) {
        continue;
      }

      try {
        const fileResult = await this.reorganizeSingleFile(
          file,
          language,
          baseData.data,
          { dryRun, backupFiles, preserveComments }
        );

        result.fileResults[language] = fileResult;

        if (fileResult.reorganized) {
          result.summary.filesReorganized++;
          result.summary.keysAdded += fileResult.changes.keysAdded;
          result.summary.keysRemoved += fileResult.changes.keysRemoved;
        } else {
          result.summary.filesSkipped++;
        }

        if (fileResult.error) {
          result.summary.errors++;
          result.errors.push(`${language}: ${fileResult.error}`);
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        result.summary.errors++;
        result.errors.push(`${language}: ${errorMsg}`);
        result.fileResults[language] = {
          language,
          filePath: file,
          reorganized: false,
          skipReason: 'Error during processing',
          changes: { keysAdded: 0, keysRemoved: 0, keysReordered: false },
          error: errorMsg
        };
      }
    }

    // Generate recommendations
    result.recommendations = this.generateRecommendations(result, dryRun);

    // Update overall success status
    result.success = result.summary.errors === 0;

    return result;
  }

  /**
   * Reorganize a single translation file
   */
  private async reorganizeSingleFile(
    filePath: string,
    language: string,
    baseStructure: any,
    options: { dryRun: boolean; backupFiles: boolean; preserveComments: boolean }
  ): Promise<FileReorganizationResult> {
    const result: FileReorganizationResult = {
      language,
      filePath,
      reorganized: false,
      changes: { keysAdded: 0, keysRemoved: 0, keysReordered: false }
    };

    // Load current file
    const currentData = await this.loadTranslationFile(filePath);

    if (!currentData.validJson) {
      result.skipReason = 'Invalid JSON syntax';
      result.error = currentData.parseError;
      return result;
    }

    const currentTranslations = currentData.data || {};

    // Create reorganized structure based on base language
    const reorganizedStructure = this.createReorganizedStructure(
      baseStructure,
      currentTranslations
    );

    // Calculate changes
    const currentPaths = ObjectManipulator.getAllPaths(currentTranslations);
    const newPaths = ObjectManipulator.getAllPaths(reorganizedStructure);

    result.changes.keysAdded = newPaths.filter(path => !currentPaths.includes(path)).length;
    result.changes.keysRemoved = currentPaths.filter(path => !newPaths.includes(path)).length;
    result.changes.keysReordered = this.hasKeyOrderChanged(currentTranslations, reorganizedStructure);

    // Check if any changes are needed
    const hasChanges = result.changes.keysAdded > 0 ||
                      result.changes.keysRemoved > 0 ||
                      result.changes.keysReordered;

    if (!hasChanges) {
      result.skipReason = 'No changes needed';
      return result;
    }

    // If not dry run, write the reorganized file
    if (!options.dryRun) {
      try {
        // Create backup if requested
        if (options.backupFiles) {
          await this.createBackupFile(filePath);
        }

        // Write reorganized file
        await JsonOperations.writeFile(filePath, reorganizedStructure, 2);

        result.reorganized = true;
      } catch (error) {
        result.error = error instanceof Error ? error.message : 'Unknown error writing file';
        return result;
      }
    } else {
      // For dry run, mark as reorganized to show what would happen
      result.reorganized = true;
    }

    return result;
  }

  /**
   * Create reorganized structure based on base language template
   */
  private createReorganizedStructure(baseStructure: any, currentTranslations: any): any {
    const reorganized: any = {};

    // Recursively build structure following base language order
    this.buildStructureRecursively(baseStructure, currentTranslations, reorganized);

    return reorganized;
  }

  /**
   * Recursively build structure maintaining base language key order
   */
  private buildStructureRecursively(
    baseObj: any,
    currentObj: any,
    targetObj: any,
    currentPath: string = ''
  ): void {
    if (!baseObj || typeof baseObj !== 'object' || Array.isArray(baseObj)) {
      return;
    }

    // Process keys in the order they appear in base language
    for (const [key, baseValue] of Object.entries(baseObj)) {
      const keyPath = currentPath ? `${currentPath}.${key}` : key;

      if (baseValue && typeof baseValue === 'object' && !Array.isArray(baseValue)) {
        // Nested object - recurse
        targetObj[key] = {};
        this.buildStructureRecursively(
          baseValue,
          currentObj?.[key] || {},
          targetObj[key],
          keyPath
        );
      } else {
        // Leaf node - use current translation if available, otherwise use base value as placeholder
        if (currentObj && typeof currentObj === 'object' && key in currentObj) {
          targetObj[key] = currentObj[key];
        } else {
          // Use base value as placeholder (this will be a missing translation)
          targetObj[key] = baseValue;
        }
      }
    }
  }

  /**
   * Check if key order has changed between two objects
   */
  private hasKeyOrderChanged(obj1: any, obj2: any): boolean {
    if (!obj1 || !obj2 || typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return false;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    // If different number of keys, order has changed
    if (keys1.length !== keys2.length) {
      return true;
    }

    // Check if keys are in same order
    for (let i = 0; i < keys1.length; i++) {
      if (keys1[i] !== keys2[i]) {
        return true;
      }
    }

    // Recursively check nested objects
    for (const key of keys1) {
      if (key in obj2) {
        const val1 = obj1[key];
        const val2 = obj2[key];

        if (val1 && val2 && typeof val1 === 'object' && typeof val2 === 'object' &&
            !Array.isArray(val1) && !Array.isArray(val2)) {
          if (this.hasKeyOrderChanged(val1, val2)) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * Create backup file with timestamp
   */
  private async createBackupFile(filePath: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup-${timestamp}`;

    try {
      await fs.copyFile(filePath, backupPath);
    } catch (error) {
      throw new Error(`Failed to create backup file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Load and parse a translation file
   */
  private async loadTranslationFile(filePath: string): Promise<{
    validJson: boolean;
    data?: any;
    parseError?: string;
  }> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(content);
      return { validJson: true, data };
    } catch (error) {
      return {
        validJson: false,
        parseError: error instanceof Error ? error.message : 'Unknown parse error'
      };
    }
  }

  /**
   * Get all translation files in the directory
   */
  private async getTranslationFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.config.translationDir);
      return files
        .filter(file => extname(file) === '.json')
        .map(file => join(this.config.translationDir, file));
    } catch (error) {
      throw new Error(`Cannot read translation directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract language code from filename
   */
  private extractLanguageFromFilename(filePath: string): string {
    const filename = filePath.split('/').pop() || '';
    return filename.replace('.json', '');
  }

  /**
   * Generate recommendations based on reorganization results
   */
  private generateRecommendations(result: ReorganizationResult, dryRun: boolean): string[] {
    const recommendations: string[] = [];

    if (result.summary.errors > 0) {
      recommendations.push(`🚨 ${result.summary.errors} file${result.summary.errors > 1 ? 's' : ''} had errors during reorganization`);
    }

    if (result.summary.filesReorganized > 0) {
      if (dryRun) {
        recommendations.push(`📋 ${result.summary.filesReorganized} file${result.summary.filesReorganized > 1 ? 's' : ''} would be reorganized (dry run mode)`);
      } else {
        recommendations.push(`✅ Successfully reorganized ${result.summary.filesReorganized} file${result.summary.filesReorganized > 1 ? 's' : ''}`);
      }
    }

    if (result.summary.keysAdded > 0) {
      recommendations.push(`📝 ${result.summary.keysAdded} missing key${result.summary.keysAdded > 1 ? 's' : ''} ${dryRun ? 'would be' : 'were'} added with base language values as placeholders`);
    }

    if (result.summary.keysRemoved > 0) {
      recommendations.push(`🗑️ ${result.summary.keysRemoved} extra key${result.summary.keysRemoved > 1 ? 's' : ''} ${dryRun ? 'would be' : 'were'} removed`);
    }

    if (result.summary.filesSkipped > 0) {
      recommendations.push(`⏭️ ${result.summary.filesSkipped} file${result.summary.filesSkipped > 1 ? 's' : ''} skipped (no changes needed or errors)`);
    }

    if (result.summary.filesReorganized === 0 && result.summary.errors === 0) {
      recommendations.push('✨ All translation files are already properly organized');
    }

    if (dryRun && result.summary.filesReorganized > 0) {
      recommendations.push('💡 Run without dryRun=true to apply these changes');
    }

    if (!dryRun && result.summary.filesReorganized > 0) {
      recommendations.push('🔄 Consider running the check_translation_integrity tool to verify the reorganization');
    }

    return recommendations;
  }
}
